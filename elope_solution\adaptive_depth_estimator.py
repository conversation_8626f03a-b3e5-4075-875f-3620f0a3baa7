#!/usr/bin/env python3
"""
Adaptive Depth Estimator for ELOPE Challenge.

This module implements dynamic depth estimation to replace the fixed depth of 1000.0,
using range meter data and optical flow consistency for improved motion estimation.
"""

import numpy as np
import cv2
from typing import Dict, List, Tuple, Any, Optional
from scipy import ndimage
from scipy.interpolate import interp1d
import warnings
warnings.filterwarnings('ignore')

class AdaptiveDepthEstimator:
    """
    Adaptive depth estimator using range meter data and optical flow consistency.
    
    Replaces the fixed depth of 1000.0 with dynamic estimates based on:
    1. Range meter measurements
    2. Optical flow consistency
    3. Temporal smoothing
    4. Scene structure analysis
    """
    
    def __init__(self):
        """Initialize adaptive depth estimator."""
        self.depth_params = {
            # Range meter processing
            'range_weight': 0.7,  # High weight for range meter
            'range_smoothing_window': 5,
            'range_outlier_threshold': 3.0,  # MAD threshold
            
            # Optical flow depth estimation
            'flow_weight': 0.3,
            'min_flow_magnitude': 1.0,
            'flow_consistency_threshold': 0.8,
            
            # Temporal smoothing
            'temporal_weight': 0.4,
            'spatial_weight': 0.6,
            
            # Depth bounds
            'min_depth': 100.0,   # Minimum reasonable depth
            'max_depth': 5000.0,  # Maximum reasonable depth
            'default_depth': 1000.0  # Fallback depth
        }
        
        # Depth estimation history
        self.depth_history = []
        self.confidence_history = []
        
        # Range meter data cache
        self.range_cache = []
        
    def estimate_adaptive_depth(self, range_data: np.ndarray,
                               flow: np.ndarray,
                               frame1: np.ndarray,
                               frame2: np.ndarray,
                               timestamp: float,
                               previous_depth: Optional[float] = None) -> Dict[str, Any]:
        """
        Estimate adaptive depth using multiple sources.
        
        Args:
            range_data: Range meter measurements
            flow: Optical flow field
            frame1: First frame
            frame2: Second frame
            timestamp: Current timestamp
            previous_depth: Previous depth estimate for temporal consistency
            
        Returns:
            Dictionary with depth estimate and confidence metrics
        """
        # Method 1: Range meter based depth
        range_depth, range_confidence = self._estimate_depth_from_range(range_data, timestamp)
        
        # Method 2: Optical flow based depth
        flow_depth, flow_confidence = self._estimate_depth_from_flow(flow, frame1, frame2)
        
        # Method 3: Scene structure based depth
        structure_depth, structure_confidence = self._estimate_depth_from_structure(frame1, frame2)
        
        # Intelligent fusion of depth estimates
        fused_depth, combined_confidence = self._fuse_depth_estimates(
            range_depth, range_confidence,
            flow_depth, flow_confidence,
            structure_depth, structure_confidence,
            previous_depth
        )
        
        # Apply temporal smoothing
        smoothed_depth = self._apply_temporal_smoothing(fused_depth, combined_confidence)
        
        # Update history
        self._update_depth_history(smoothed_depth, combined_confidence)
        
        return {
            'depth_estimate': smoothed_depth,
            'depth_confidence': combined_confidence,
            'depth_components': {
                'range': range_depth,
                'flow': flow_depth,
                'structure': structure_depth
            },
            'depth_quality_metrics': self._compute_depth_quality_metrics(range_data, flow)
        }
    
    def _estimate_depth_from_range(self, range_data: np.ndarray, timestamp: float) -> Tuple[float, float]:
        """Estimate depth from range meter data."""
        
        if len(range_data) == 0:
            return self.depth_params['default_depth'], 0.0
        
        # Cache range data for temporal processing
        self.range_cache.append({'timestamp': timestamp, 'range': np.mean(range_data)})
        
        # Keep only recent data
        max_cache_size = 20
        if len(self.range_cache) > max_cache_size:
            self.range_cache = self.range_cache[-max_cache_size:]
        
        # Extract recent range measurements
        recent_ranges = [item['range'] for item in self.range_cache[-self.depth_params['range_smoothing_window']:]]
        
        if len(recent_ranges) < 2:
            return np.mean(recent_ranges) if recent_ranges else self.depth_params['default_depth'], 0.5
        
        # Robust outlier removal using MAD
        median_range = np.median(recent_ranges)
        mad = np.median(np.abs(np.array(recent_ranges) - median_range))
        
        if mad > 0:
            outlier_mask = np.abs(np.array(recent_ranges) - median_range) < self.depth_params['range_outlier_threshold'] * mad
            filtered_ranges = np.array(recent_ranges)[outlier_mask]
        else:
            filtered_ranges = np.array(recent_ranges)
        
        if len(filtered_ranges) == 0:
            return self.depth_params['default_depth'], 0.0
        
        # Robust depth estimate
        range_depth = np.median(filtered_ranges)
        
        # Confidence based on consistency and data quality
        range_std = np.std(filtered_ranges)
        consistency = 1.0 / (1.0 + range_std / median_range)  # Relative consistency
        data_quality = len(filtered_ranges) / len(recent_ranges)  # Outlier ratio
        
        range_confidence = consistency * data_quality * 0.9  # High confidence for range meter
        
        # Clamp to reasonable bounds
        range_depth = np.clip(range_depth, self.depth_params['min_depth'], self.depth_params['max_depth'])
        
        return range_depth, min(range_confidence, 1.0)
    
    def _estimate_depth_from_flow(self, flow: np.ndarray, frame1: np.ndarray, frame2: np.ndarray) -> Tuple[float, float]:
        """Estimate depth from optical flow consistency."""
        
        # Calculate flow magnitude
        flow_magnitude = np.linalg.norm(flow, axis=2)
        
        # Filter significant flow
        significant_mask = flow_magnitude > self.depth_params['min_flow_magnitude']
        
        if np.sum(significant_mask) < 10:
            return self.depth_params['default_depth'], 0.0
        
        # Extract flow vectors
        significant_flow = flow[significant_mask]
        flow_magnitudes = flow_magnitude[significant_mask]
        
        # Estimate depth from flow divergence (simplified model)
        # Larger flow typically indicates closer objects
        mean_flow_magnitude = np.mean(flow_magnitudes)
        
        # Empirical relationship: depth inversely related to flow magnitude
        # This is a simplified model - in practice would need calibration
        if mean_flow_magnitude > 0:
            flow_depth = 2000.0 / (1.0 + mean_flow_magnitude / 10.0)
        else:
            flow_depth = self.depth_params['default_depth']
        
        # Confidence based on flow consistency
        flow_std = np.std(flow_magnitudes)
        flow_consistency = 1.0 / (1.0 + flow_std / (mean_flow_magnitude + 1e-6))
        
        # Flow-based depth has lower confidence than range meter
        flow_confidence = flow_consistency * 0.6
        
        # Clamp to reasonable bounds
        flow_depth = np.clip(flow_depth, self.depth_params['min_depth'], self.depth_params['max_depth'])
        
        return flow_depth, min(flow_confidence, 1.0)
    
    def _estimate_depth_from_structure(self, frame1: np.ndarray, frame2: np.ndarray) -> Tuple[float, float]:
        """Estimate depth from scene structure analysis."""
        
        # Convert to grayscale if needed
        gray1 = self._to_grayscale(frame1)
        gray2 = self._to_grayscale(frame2)
        
        # Analyze image gradients for structure
        grad_x1 = cv2.Sobel(gray1, cv2.CV_64F, 1, 0, ksize=3)
        grad_y1 = cv2.Sobel(gray1, cv2.CV_64F, 0, 1, ksize=3)
        gradient_magnitude1 = np.sqrt(grad_x1**2 + grad_y1**2)
        
        # High gradient regions typically indicate closer objects
        high_gradient_mask = gradient_magnitude1 > np.percentile(gradient_magnitude1, 75)
        
        if np.sum(high_gradient_mask) < 10:
            return self.depth_params['default_depth'], 0.0
        
        # Estimate structure-based depth
        mean_gradient = np.mean(gradient_magnitude1[high_gradient_mask])
        
        # Empirical relationship: higher gradients suggest closer objects
        if mean_gradient > 0:
            structure_depth = 1500.0 / (1.0 + mean_gradient / 50.0)
        else:
            structure_depth = self.depth_params['default_depth']
        
        # Confidence based on gradient strength and distribution
        gradient_std = np.std(gradient_magnitude1[high_gradient_mask])
        structure_consistency = 1.0 / (1.0 + gradient_std / (mean_gradient + 1e-6))
        
        # Structure-based depth has moderate confidence
        structure_confidence = structure_consistency * 0.5
        
        # Clamp to reasonable bounds
        structure_depth = np.clip(structure_depth, self.depth_params['min_depth'], self.depth_params['max_depth'])
        
        return structure_depth, min(structure_confidence, 1.0)
    
    def _fuse_depth_estimates(self, range_depth: float, range_confidence: float,
                             flow_depth: float, flow_confidence: float,
                             structure_depth: float, structure_confidence: float,
                             previous_depth: Optional[float]) -> Tuple[float, float]:
        """Intelligently fuse multiple depth estimates."""
        
        estimates = []
        weights = []
        confidences = []
        
        # Range meter estimate (highest priority)
        if range_confidence > 0.1:
            estimates.append(range_depth)
            weights.append(range_confidence * self.depth_params['range_weight'])
            confidences.append(range_confidence)
        
        # Optical flow estimate
        if flow_confidence > 0.1:
            estimates.append(flow_depth)
            weights.append(flow_confidence * self.depth_params['flow_weight'])
            confidences.append(flow_confidence)
        
        # Structure estimate
        if structure_confidence > 0.1:
            estimates.append(structure_depth)
            weights.append(structure_confidence * 0.3)  # Lower weight
            confidences.append(structure_confidence)
        
        # Temporal consistency with previous estimate
        if previous_depth is not None and len(estimates) > 0:
            estimates.append(previous_depth)
            weights.append(self.depth_params['temporal_weight'])
            confidences.append(0.7)  # Moderate confidence in temporal consistency
        
        if len(estimates) == 0:
            return self.depth_params['default_depth'], 0.0
        
        # Normalize weights
        weights = np.array(weights)
        weights = weights / np.sum(weights)
        
        # Weighted fusion
        fused_depth = np.average(estimates, weights=weights)
        
        # Combined confidence
        combined_confidence = np.average(confidences, weights=weights)
        
        return fused_depth, combined_confidence
    
    def _apply_temporal_smoothing(self, current_depth: float, confidence: float) -> float:
        """Apply temporal smoothing to depth estimates."""
        
        if len(self.depth_history) < 2:
            return current_depth
        
        # Use recent history for smoothing
        recent_depths = self.depth_history[-3:]  # Last 3 estimates
        recent_confidences = self.confidence_history[-3:]
        
        # Add current estimate
        recent_depths.append(current_depth)
        recent_confidences.append(confidence)
        
        # Weighted temporal smoothing
        weights = np.array(recent_confidences)
        weights = weights / np.sum(weights)
        
        smoothed_depth = np.average(recent_depths, weights=weights)
        
        return smoothed_depth
    
    def _update_depth_history(self, depth_estimate: float, confidence: float):
        """Update depth estimation history."""
        
        self.depth_history.append(depth_estimate)
        self.confidence_history.append(confidence)
        
        # Keep only recent history
        max_history = 10
        if len(self.depth_history) > max_history:
            self.depth_history = self.depth_history[-max_history:]
            self.confidence_history = self.confidence_history[-max_history:]
    
    def _compute_depth_quality_metrics(self, range_data: np.ndarray, flow: np.ndarray) -> Dict[str, float]:
        """Compute quality metrics for depth estimation."""
        
        # Range data quality
        range_quality = 1.0 if len(range_data) > 0 else 0.0
        if len(range_data) > 1:
            range_std = np.std(range_data)
            range_quality = 1.0 / (1.0 + range_std / (np.mean(range_data) + 1e-6))
        
        # Flow quality
        flow_magnitude = np.linalg.norm(flow, axis=2)
        flow_coverage = np.sum(flow_magnitude > 1.0) / flow.size
        flow_consistency = 1.0 / (1.0 + np.std(flow_magnitude) / (np.mean(flow_magnitude) + 1e-6))
        
        return {
            'range_quality': range_quality,
            'flow_coverage': flow_coverage,
            'flow_consistency': flow_consistency,
            'overall_quality': (range_quality + flow_coverage + flow_consistency) / 3.0
        }
    
    def _to_grayscale(self, frame: np.ndarray) -> np.ndarray:
        """Convert frame to grayscale if needed."""
        if len(frame.shape) == 3:
            return cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        return frame
    
    def get_depth_statistics(self) -> Dict[str, Any]:
        """Get statistics about depth estimation performance."""
        
        if len(self.depth_history) == 0:
            return {'status': 'no_data'}
        
        depths = np.array(self.depth_history)
        confidences = np.array(self.confidence_history)
        
        return {
            'mean_depth': np.mean(depths),
            'std_depth': np.std(depths),
            'min_depth': np.min(depths),
            'max_depth': np.max(depths),
            'mean_confidence': np.mean(confidences),
            'depth_stability': 1.0 / (1.0 + np.std(depths) / np.mean(depths)),
            'num_estimates': len(depths)
        }
