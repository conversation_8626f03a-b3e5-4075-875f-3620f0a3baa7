#!/usr/bin/env python3
"""
ELOPE Dataset Feature Mining Script
分析ELOPE数据集特征，发现隐藏的模式、约束条件和数据质量问题
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json
from typing import Dict, List, Tuple, Any
from scipy import stats
import h5py

class ELOPEDataMiner:
    """ELOPE数据集特征挖掘器"""
    
    def __init__(self, data_dir: str = "../data"):
        """初始化数据挖掘器"""
        self.data_dir = Path(data_dir)
        self.sequences_analyzed = 0
        self.feature_patterns = {}
        
    def analyze_sequence_metadata(self) -> Dict[str, Any]:
        """分析序列元数据模式"""
        print("🔍 序列元数据分析")
        print("=" * 50)
        
        # 从验证报告中提取元数据
        with open('full_training_validation_report.json', 'r', encoding='utf-8') as f:
            report = json.load(f)
        
        sequences = report['sequence_details']
        
        # 提取关键特征
        seq_ids = [seq['sequence_id'] for seq in sequences]
        rmse_values = [seq['rmse'] for seq in sequences]
        processing_times = [seq['processing_time'] for seq in sequences]
        data_lengths = [seq['data_length'] for seq in sequences]
        events_counts = [seq['events_count'] for seq in sequences]
        
        # 计算事件密度
        event_densities = [events / length for events, length in zip(events_counts, data_lengths)]
        
        # 计算处理效率 (events per second)
        processing_efficiency = [events / time for events, time in zip(events_counts, processing_times)]
        
        print(f"数据集基本统计:")
        print(f"  序列数量: {len(sequences)}")
        print(f"  数据长度: {data_lengths[0]} (所有序列相同)")
        print(f"  事件数量范围: [{min(events_counts):,}, {max(events_counts):,}]")
        print(f"  事件密度范围: [{min(event_densities):.0f}, {max(event_densities):.0f}] events/frame")
        print(f"  处理时间范围: [{min(processing_times):.1f}, {max(processing_times):.1f}] seconds")
        
        # 事件数量分布分析
        events_stats = {
            'mean': np.mean(events_counts),
            'std': np.std(events_counts),
            'median': np.median(events_counts),
            'q25': np.percentile(events_counts, 25),
            'q75': np.percentile(events_counts, 75),
            'skewness': stats.skew(events_counts),
            'kurtosis': stats.kurtosis(events_counts)
        }
        
        print(f"\n事件数量分布特征:")
        print(f"  平均值: {events_stats['mean']:,.0f}")
        print(f"  标准差: {events_stats['std']:,.0f}")
        print(f"  偏度: {events_stats['skewness']:.3f} ({'右偏' if events_stats['skewness'] > 0 else '左偏'})")
        print(f"  峰度: {events_stats['kurtosis']:.3f}")
        
        # 识别事件数量异常序列
        q1, q3 = events_stats['q25'], events_stats['q75']
        iqr = q3 - q1
        outlier_threshold_low = q1 - 1.5 * iqr
        outlier_threshold_high = q3 + 1.5 * iqr
        
        outlier_sequences = []
        for i, events in enumerate(events_counts):
            if events < outlier_threshold_low or events > outlier_threshold_high:
                outlier_sequences.append({
                    'seq_id': seq_ids[i],
                    'events_count': events,
                    'type': 'high' if events > outlier_threshold_high else 'low',
                    'rmse': rmse_values[i]
                })
        
        print(f"\n事件数量异常序列: {len(outlier_sequences)}")
        for outlier in outlier_sequences:
            print(f"  序列{outlier['seq_id']}: {outlier['events_count']:,} events ({outlier['type']}) - RMSE: {outlier['rmse']:.1f}")
        
        return {
            'basic_stats': {
                'sequence_count': len(sequences),
                'data_length': data_lengths[0],
                'events_range': [min(events_counts), max(events_counts)],
                'density_range': [min(event_densities), max(event_densities)]
            },
            'events_stats': events_stats,
            'outlier_sequences': outlier_sequences,
            'event_densities': event_densities,
            'processing_efficiency': processing_efficiency
        }
    
    def analyze_performance_patterns(self) -> Dict[str, Any]:
        """分析性能模式和数据质量关系"""
        print("\n🔍 性能模式分析")
        print("=" * 50)
        
        # 加载验证报告
        with open('full_training_validation_report.json', 'r', encoding='utf-8') as f:
            report = json.load(f)
        
        sequences = report['sequence_details']
        
        # 提取性能数据
        rmse_values = [seq['rmse'] for seq in sequences]
        vx_errors = [seq['component_rmse']['vx'] for seq in sequences]
        vy_errors = [seq['component_rmse']['vy'] for seq in sequences]
        vz_errors = [seq['component_rmse']['vz'] for seq in sequences]
        events_counts = [seq['events_count'] for seq in sequences]
        processing_times = [seq['processing_time'] for seq in sequences]
        
        # 性能分组分析
        performance_groups = {
            'excellent': [],  # RMSE < 110
            'good': [],       # 110 <= RMSE < 130
            'poor': []        # RMSE >= 130
        }
        
        for i, rmse in enumerate(rmse_values):
            seq_data = {
                'seq_id': sequences[i]['sequence_id'],
                'rmse': rmse,
                'events': events_counts[i],
                'time': processing_times[i],
                'vy_error': vy_errors[i]
            }
            
            if rmse < 110:
                performance_groups['excellent'].append(seq_data)
            elif rmse < 130:
                performance_groups['good'].append(seq_data)
            else:
                performance_groups['poor'].append(seq_data)
        
        print(f"性能分组:")
        for group, seqs in performance_groups.items():
            if seqs:
                avg_events = np.mean([s['events'] for s in seqs])
                avg_time = np.mean([s['time'] for s in seqs])
                avg_vy = np.mean([s['vy_error'] for s in seqs])
                print(f"  {group.upper()} ({len(seqs)}个序列):")
                print(f"    平均事件数: {avg_events:,.0f}")
                print(f"    平均处理时间: {avg_time:.1f}s")
                print(f"    平均VY误差: {avg_vy:.1f}")
        
        # 事件数量与性能的关系
        events_performance_corr, events_performance_p = stats.pearsonr(events_counts, rmse_values)
        print(f"\n事件数量与RMSE相关性: r={events_performance_corr:.3f}, p={events_performance_p:.3f}")
        
        # VY误差与事件数量的关系
        vy_events_corr, vy_events_p = stats.pearsonr(vy_errors, events_counts)
        print(f"VY误差与事件数量相关性: r={vy_events_corr:.3f}, p={vy_events_p:.3f}")
        
        return {
            'performance_groups': performance_groups,
            'correlations': {
                'events_rmse': {'r': events_performance_corr, 'p': events_performance_p},
                'vy_events': {'r': vy_events_corr, 'p': vy_events_p}
            }
        }
    
    def analyze_sequence_difficulty_patterns(self) -> Dict[str, Any]:
        """分析序列难度模式"""
        print("\n🔍 序列难度模式分析")
        print("=" * 50)
        
        # 加载验证报告
        with open('full_training_validation_report.json', 'r', encoding='utf-8') as f:
            report = json.load(f)
        
        # 从报告中获取难度分类
        difficulty_analysis = report['performance_distribution']['difficulty_analysis']
        
        easy_seqs = difficulty_analysis['easy_sequences']
        medium_seqs = difficulty_analysis['medium_sequences']
        hard_seqs = difficulty_analysis['hard_sequences']
        
        print(f"难度分布:")
        print(f"  简单序列: {len(easy_seqs)} ({len(easy_seqs)/28*100:.1f}%)")
        print(f"  中等序列: {len(medium_seqs)} ({len(medium_seqs)/28*100:.1f}%)")
        print(f"  困难序列: {len(hard_seqs)} ({len(hard_seqs)/28*100:.1f}%)")
        
        # 分析每个难度组的特征
        sequences = report['sequence_details']
        
        difficulty_features = {}
        for difficulty, seq_ids in [('easy', easy_seqs), ('medium', medium_seqs), ('hard', hard_seqs)]:
            group_data = [seq for seq in sequences if seq['sequence_id'] in seq_ids]
            
            if group_data:
                features = {
                    'avg_rmse': np.mean([seq['rmse'] for seq in group_data]),
                    'avg_events': np.mean([seq['events_count'] for seq in group_data]),
                    'avg_vy_error': np.mean([seq['component_rmse']['vy'] for seq in group_data]),
                    'avg_processing_time': np.mean([seq['processing_time'] for seq in group_data])
                }
                difficulty_features[difficulty] = features
                
                print(f"\n{difficulty.upper()}序列特征:")
                print(f"  平均RMSE: {features['avg_rmse']:.1f}")
                print(f"  平均事件数: {features['avg_events']:,.0f}")
                print(f"  平均VY误差: {features['avg_vy_error']:.1f}")
                print(f"  平均处理时间: {features['avg_processing_time']:.1f}s")
        
        return {
            'difficulty_distribution': {
                'easy': len(easy_seqs),
                'medium': len(medium_seqs),
                'hard': len(hard_seqs)
            },
            'difficulty_features': difficulty_features,
            'sequence_groups': {
                'easy': easy_seqs,
                'medium': medium_seqs,
                'hard': hard_seqs
            }
        }
    
    def identify_data_quality_issues(self) -> Dict[str, Any]:
        """识别数据质量问题"""
        print("\n🔍 数据质量问题识别")
        print("=" * 50)
        
        # 加载验证报告
        with open('full_training_validation_report.json', 'r', encoding='utf-8') as f:
            report = json.load(f)
        
        sequences = report['sequence_details']
        
        quality_issues = []
        
        # 检查事件数量异常
        events_counts = [seq['events_count'] for seq in sequences]
        events_mean = np.mean(events_counts)
        events_std = np.std(events_counts)
        
        for seq in sequences:
            seq_id = seq['sequence_id']
            events = seq['events_count']
            
            # 事件数量过少或过多
            if events < events_mean - 2 * events_std:
                quality_issues.append({
                    'seq_id': seq_id,
                    'issue': 'low_event_count',
                    'value': events,
                    'severity': 'medium'
                })
            elif events > events_mean + 2 * events_std:
                quality_issues.append({
                    'seq_id': seq_id,
                    'issue': 'high_event_count',
                    'value': events,
                    'severity': 'low'
                })
            
            # 处理时间异常
            processing_time = seq['processing_time']
            if processing_time > 100:  # 超过100秒
                quality_issues.append({
                    'seq_id': seq_id,
                    'issue': 'long_processing_time',
                    'value': processing_time,
                    'severity': 'low'
                })
            
            # VY误差极端值
            vy_error = seq['component_rmse']['vy']
            if vy_error > 120:  # VY误差过高
                quality_issues.append({
                    'seq_id': seq_id,
                    'issue': 'extreme_vy_error',
                    'value': vy_error,
                    'severity': 'high'
                })
            elif vy_error < 30:  # VY误差异常低
                quality_issues.append({
                    'seq_id': seq_id,
                    'issue': 'unusually_low_vy_error',
                    'value': vy_error,
                    'severity': 'medium'
                })
        
        # 按严重程度分组
        severity_groups = {'high': [], 'medium': [], 'low': []}
        for issue in quality_issues:
            severity_groups[issue['severity']].append(issue)
        
        print(f"数据质量问题统计:")
        for severity, issues in severity_groups.items():
            print(f"  {severity.upper()}严重程度: {len(issues)}个问题")
            for issue in issues:
                print(f"    序列{issue['seq_id']}: {issue['issue']} (值: {issue['value']})")
        
        return {
            'quality_issues': quality_issues,
            'severity_groups': severity_groups,
            'total_issues': len(quality_issues)
        }
    
    def generate_comprehensive_report(self) -> Dict[str, Any]:
        """生成综合数据挖掘报告"""
        print("🚀 ELOPE数据集特征挖掘报告")
        print("=" * 60)
        
        # 执行所有分析
        metadata_analysis = self.analyze_sequence_metadata()
        performance_analysis = self.analyze_performance_patterns()
        difficulty_analysis = self.analyze_sequence_difficulty_patterns()
        quality_analysis = self.identify_data_quality_issues()
        
        # 综合结论
        print("\n📊 数据挖掘综合结论")
        print("=" * 50)
        
        print("1. 数据集特征:")
        print(f"   - 事件数量变化范围极大: {metadata_analysis['basic_stats']['events_range'][1]/metadata_analysis['basic_stats']['events_range'][0]:.1f}倍差异")
        print(f"   - 事件分布呈现{'右偏' if metadata_analysis['events_stats']['skewness'] > 0 else '左偏'}特征")
        
        print("2. 性能模式:")
        excellent_count = len(performance_analysis['performance_groups']['excellent'])
        poor_count = len(performance_analysis['performance_groups']['poor'])
        print(f"   - 优秀序列: {excellent_count}个, 差序列: {poor_count}个")
        
        events_rmse_corr = performance_analysis['correlations']['events_rmse']['r']
        if abs(events_rmse_corr) > 0.3:
            print(f"   - 事件数量与性能存在{'正' if events_rmse_corr > 0 else '负'}相关性 (r={events_rmse_corr:.3f})")
        
        print("3. 数据质量:")
        high_severity_issues = len(quality_analysis['severity_groups']['high'])
        if high_severity_issues > 0:
            print(f"   - 发现 {high_severity_issues} 个高严重程度数据质量问题")
        else:
            print("   - 未发现严重数据质量问题")
        
        return {
            'metadata_analysis': metadata_analysis,
            'performance_analysis': performance_analysis,
            'difficulty_analysis': difficulty_analysis,
            'quality_analysis': quality_analysis
        }

def main():
    """主函数"""
    miner = ELOPEDataMiner()
    
    # 生成综合分析报告
    report = miner.generate_comprehensive_report()
    
    # 保存分析结果
    output_path = Path('data_feature_mining_report.json')
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"\n✅ 数据挖掘完成！详细报告已保存到: {output_path}")

if __name__ == "__main__":
    main()
