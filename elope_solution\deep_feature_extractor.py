#!/usr/bin/env python3
"""
Deep Learning Feature Extractor for ELOPE Challenge.

This module implements a lightweight CNN-based feature extractor to replace
traditional corner detection methods, providing better feature quality and
density for event camera data.
"""

import numpy as np
import cv2
from typing import Dict, List, Tuple, Any, Optional
import warnings
warnings.filterwarnings('ignore')

# Try to import deep learning libraries
try:
    import torch
    import torch.nn as nn
    import torch.nn.functional as F
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    print("PyTorch not available, using traditional feature extraction")

class LightweightFeatureNet(nn.Module):
    """
    Lightweight CNN for feature extraction from event frames.
    
    Architecture optimized for:
    1. Real-time performance
    2. Event camera data characteristics
    3. Dense feature extraction
    4. Robust corner/edge detection
    """
    
    def __init__(self, input_channels=1, feature_dim=128):
        super(LightweightFeatureNet, self).__init__()
        
        # Feature extraction backbone
        self.conv1 = nn.Conv2d(input_channels, 32, kernel_size=3, padding=1)
        self.conv2 = nn.Conv2d(32, 64, kernel_size=3, padding=1)
        self.conv3 = nn.Conv2d(64, 128, kernel_size=3, padding=1)
        
        # Feature refinement
        self.conv4 = nn.Conv2d(128, 64, kernel_size=1)
        self.conv5 = nn.Conv2d(64, 32, kernel_size=1)
        
        # Feature descriptor head
        self.descriptor_head = nn.Conv2d(32, feature_dim, kernel_size=1)
        
        # Keypoint detection head
        self.keypoint_head = nn.Conv2d(32, 1, kernel_size=1)
        
        # Batch normalization
        self.bn1 = nn.BatchNorm2d(32)
        self.bn2 = nn.BatchNorm2d(64)
        self.bn3 = nn.BatchNorm2d(128)
        self.bn4 = nn.BatchNorm2d(64)
        self.bn5 = nn.BatchNorm2d(32)
        
        # Dropout for regularization
        self.dropout = nn.Dropout2d(0.1)
        
    def forward(self, x):
        # Feature extraction
        x1 = F.relu(self.bn1(self.conv1(x)))
        x2 = F.relu(self.bn2(self.conv2(x1)))
        x3 = F.relu(self.bn3(self.conv3(x2)))
        
        # Feature refinement
        x4 = F.relu(self.bn4(self.conv4(x3)))
        x5 = F.relu(self.bn5(self.conv5(x4)))
        x5 = self.dropout(x5)
        
        # Generate outputs
        descriptors = self.descriptor_head(x5)
        keypoints = torch.sigmoid(self.keypoint_head(x5))
        
        return {
            'descriptors': descriptors,
            'keypoints': keypoints,
            'features': x5
        }

class DeepFeatureExtractor:
    """
    Deep learning-based feature extractor for enhanced optical flow.
    
    Provides:
    1. Dense feature extraction
    2. Learned feature descriptors
    3. Adaptive keypoint detection
    4. Robust feature matching
    """
    
    def __init__(self):
        """Initialize deep feature extractor."""
        
        self.torch_available = TORCH_AVAILABLE
        
        # Feature extraction parameters
        self.feature_params = {
            # Network parameters
            'feature_dim': 128,
            'input_size': (256, 256),  # Resize input for consistent processing
            'batch_size': 1,
            
            # Keypoint detection parameters
            'keypoint_threshold': 0.3,
            'max_keypoints': 1000,
            'nms_radius': 4,
            
            # Feature matching parameters
            'match_threshold': 0.8,
            'cross_check': True,
            
            # Fallback parameters (when PyTorch unavailable)
            'fallback_max_corners': 800,
            'fallback_quality_level': 0.005,
            'fallback_min_distance': 6
        }
        
        # Initialize network if PyTorch is available
        if self.torch_available:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
            self.net = LightweightFeatureNet(
                input_channels=1,
                feature_dim=self.feature_params['feature_dim']
            ).to(self.device)
            
            # Load pretrained weights if available (would be trained separately)
            self._initialize_weights()
            
            self.net.eval()  # Set to evaluation mode
        
        # Feature cache for temporal consistency
        self.feature_cache = {}
        self.descriptor_cache = {}
        
    def _initialize_weights(self):
        """Initialize network weights with reasonable defaults."""
        for m in self.net.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)
    
    def extract_features_deep(self, frame: np.ndarray, frame_id: str = None) -> Dict[str, Any]:
        """
        Extract features using deep learning approach.
        
        Args:
            frame: Input frame (grayscale)
            frame_id: Optional frame identifier for caching
            
        Returns:
            Dictionary with keypoints, descriptors, and metadata
        """
        
        if not self.torch_available:
            return self._extract_features_fallback(frame)
        
        # Preprocess frame
        processed_frame = self._preprocess_frame(frame)
        
        # Convert to tensor
        input_tensor = torch.from_numpy(processed_frame).float().unsqueeze(0).unsqueeze(0).to(self.device)
        
        # Extract features
        with torch.no_grad():
            outputs = self.net(input_tensor)
        
        # Post-process outputs
        keypoints, descriptors = self._postprocess_outputs(outputs, frame.shape)
        
        # Cache results if frame_id provided
        if frame_id:
            self.feature_cache[frame_id] = keypoints
            self.descriptor_cache[frame_id] = descriptors
        
        return {
            'keypoints': keypoints,
            'descriptors': descriptors,
            'num_features': len(keypoints),
            'extraction_method': 'deep_learning',
            'feature_quality': self._assess_feature_quality(keypoints, descriptors)
        }
    
    def _preprocess_frame(self, frame: np.ndarray) -> np.ndarray:
        """Preprocess frame for neural network input."""
        
        # Convert to grayscale if needed
        if len(frame.shape) == 3:
            frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # Resize to network input size
        target_size = self.feature_params['input_size']
        if frame.shape != target_size:
            frame = cv2.resize(frame, target_size)
        
        # Normalize to [0, 1]
        frame = frame.astype(np.float32) / 255.0
        
        # Apply histogram equalization for better contrast
        frame = cv2.equalizeHist((frame * 255).astype(np.uint8)).astype(np.float32) / 255.0
        
        return frame
    
    def _postprocess_outputs(self, outputs: Dict[str, torch.Tensor], 
                           original_shape: Tuple[int, int]) -> Tuple[np.ndarray, np.ndarray]:
        """Post-process network outputs to extract keypoints and descriptors."""
        
        keypoint_map = outputs['keypoints'].squeeze().cpu().numpy()
        descriptor_map = outputs['descriptors'].squeeze().cpu().numpy()
        
        # Apply non-maximum suppression to keypoint map
        keypoint_map = self._apply_nms(keypoint_map, self.feature_params['nms_radius'])
        
        # Extract keypoint coordinates
        keypoint_coords = np.where(keypoint_map > self.feature_params['keypoint_threshold'])
        
        if len(keypoint_coords[0]) == 0:
            return np.array([]), np.array([])
        
        # Limit number of keypoints
        max_keypoints = self.feature_params['max_keypoints']
        if len(keypoint_coords[0]) > max_keypoints:
            # Select top keypoints by response
            responses = keypoint_map[keypoint_coords]
            top_indices = np.argsort(responses)[-max_keypoints:]
            keypoint_coords = (keypoint_coords[0][top_indices], keypoint_coords[1][top_indices])
        
        # Convert coordinates to original frame scale
        scale_y = original_shape[0] / keypoint_map.shape[0]
        scale_x = original_shape[1] / keypoint_map.shape[1]
        
        keypoints = np.column_stack([
            keypoint_coords[1] * scale_x,  # x coordinates
            keypoint_coords[0] * scale_y   # y coordinates
        ]).astype(np.float32)
        
        # Extract corresponding descriptors
        descriptors = descriptor_map[:, keypoint_coords[0], keypoint_coords[1]].T
        
        return keypoints, descriptors
    
    def _apply_nms(self, keypoint_map: np.ndarray, radius: int) -> np.ndarray:
        """Apply non-maximum suppression to keypoint map."""
        
        # Create a copy for output
        suppressed = keypoint_map.copy()
        
        # Find local maxima
        for i in range(radius, keypoint_map.shape[0] - radius):
            for j in range(radius, keypoint_map.shape[1] - radius):
                center_val = keypoint_map[i, j]
                
                # Check if it's a local maximum
                local_region = keypoint_map[i-radius:i+radius+1, j-radius:j+radius+1]
                if center_val == np.max(local_region) and center_val > 0:
                    # Suppress neighbors
                    suppressed[i-radius:i+radius+1, j-radius:j+radius+1] = 0
                    suppressed[i, j] = center_val
                else:
                    suppressed[i, j] = 0
        
        return suppressed
    
    def _extract_features_fallback(self, frame: np.ndarray) -> Dict[str, Any]:
        """Fallback feature extraction using traditional methods."""
        
        # Convert to grayscale if needed
        if len(frame.shape) == 3:
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        else:
            gray = frame
        
        # Enhanced corner detection
        corners = cv2.goodFeaturesToTrack(
            gray,
            maxCorners=self.feature_params['fallback_max_corners'],
            qualityLevel=self.feature_params['fallback_quality_level'],
            minDistance=self.feature_params['fallback_min_distance'],
            useHarrisDetector=True,
            k=0.04
        )
        
        if corners is None:
            corners = np.array([])
            descriptors = np.array([])
        else:
            corners = corners.reshape(-1, 2)
            
            # Compute simple descriptors (patch-based)
            descriptors = self._compute_patch_descriptors(gray, corners)
        
        return {
            'keypoints': corners,
            'descriptors': descriptors,
            'num_features': len(corners),
            'extraction_method': 'traditional_fallback',
            'feature_quality': self._assess_feature_quality(corners, descriptors)
        }
    
    def _compute_patch_descriptors(self, gray: np.ndarray, keypoints: np.ndarray, 
                                  patch_size: int = 16) -> np.ndarray:
        """Compute simple patch-based descriptors for fallback mode."""
        
        if len(keypoints) == 0:
            return np.array([])
        
        descriptors = []
        half_patch = patch_size // 2
        
        for kp in keypoints:
            x, y = int(kp[0]), int(kp[1])
            
            # Extract patch
            if (y - half_patch >= 0 and y + half_patch < gray.shape[0] and
                x - half_patch >= 0 and x + half_patch < gray.shape[1]):
                
                patch = gray[y-half_patch:y+half_patch, x-half_patch:x+half_patch]
                
                # Normalize patch
                patch = patch.astype(np.float32)
                patch = (patch - np.mean(patch)) / (np.std(patch) + 1e-7)
                
                # Flatten to descriptor
                descriptor = patch.flatten()
                descriptors.append(descriptor)
            else:
                # Use zero descriptor for boundary keypoints
                descriptors.append(np.zeros(patch_size * patch_size))
        
        return np.array(descriptors)
    
    def _assess_feature_quality(self, keypoints: np.ndarray, descriptors: np.ndarray) -> Dict[str, float]:
        """Assess the quality of extracted features."""
        
        if len(keypoints) == 0:
            return {
                'density': 0.0,
                'distribution': 0.0,
                'distinctiveness': 0.0,
                'overall_quality': 0.0
            }
        
        # Feature density
        density = len(keypoints) / 1000.0  # Normalized by expected count
        
        # Spatial distribution (how well spread the features are)
        if len(keypoints) > 1:
            distances = []
            for i in range(len(keypoints)):
                for j in range(i+1, len(keypoints)):
                    dist = np.linalg.norm(keypoints[i] - keypoints[j])
                    distances.append(dist)
            
            mean_distance = np.mean(distances)
            distribution = min(mean_distance / 50.0, 1.0)  # Normalized
        else:
            distribution = 0.0
        
        # Descriptor distinctiveness
        if len(descriptors) > 1:
            # Compute pairwise similarities
            similarities = []
            for i in range(len(descriptors)):
                for j in range(i+1, len(descriptors)):
                    sim = np.dot(descriptors[i], descriptors[j]) / (
                        np.linalg.norm(descriptors[i]) * np.linalg.norm(descriptors[j]) + 1e-7
                    )
                    similarities.append(abs(sim))
            
            # Lower similarity = higher distinctiveness
            distinctiveness = 1.0 - np.mean(similarities)
        else:
            distinctiveness = 0.0
        
        # Overall quality
        overall_quality = (density * 0.4 + distribution * 0.3 + distinctiveness * 0.3)
        
        return {
            'density': min(density, 1.0),
            'distribution': distribution,
            'distinctiveness': max(distinctiveness, 0.0),
            'overall_quality': min(overall_quality, 1.0)
        }
    
    def match_features_deep(self, desc1: np.ndarray, desc2: np.ndarray) -> np.ndarray:
        """Match features using enhanced descriptor matching."""
        
        if len(desc1) == 0 or len(desc2) == 0:
            return np.array([])
        
        # Compute distance matrix
        if self.torch_available and len(desc1[0]) > 50:  # Use deep descriptors
            # Cosine similarity for deep descriptors
            desc1_norm = desc1 / (np.linalg.norm(desc1, axis=1, keepdims=True) + 1e-7)
            desc2_norm = desc2 / (np.linalg.norm(desc2, axis=1, keepdims=True) + 1e-7)
            similarity_matrix = np.dot(desc1_norm, desc2_norm.T)
            distance_matrix = 1.0 - similarity_matrix
        else:
            # Euclidean distance for traditional descriptors
            distance_matrix = np.linalg.norm(
                desc1[:, np.newaxis] - desc2[np.newaxis, :], axis=2
            )
        
        # Find best matches
        matches = []
        for i in range(len(desc1)):
            distances = distance_matrix[i]
            sorted_indices = np.argsort(distances)
            
            if len(sorted_indices) >= 2:
                best_dist = distances[sorted_indices[0]]
                second_best_dist = distances[sorted_indices[1]]
                
                # Lowe's ratio test
                if best_dist < self.feature_params['match_threshold'] * second_best_dist:
                    matches.append([i, sorted_indices[0]])
        
        # Cross-check if enabled
        if self.feature_params['cross_check'] and len(matches) > 0:
            matches = self._cross_check_matches(matches, distance_matrix)
        
        return np.array(matches)
    
    def _cross_check_matches(self, matches: List[List[int]], 
                           distance_matrix: np.ndarray) -> List[List[int]]:
        """Perform cross-check on matches for robustness."""
        
        cross_checked = []
        
        for match in matches:
            i, j = match
            
            # Check if j's best match is also i
            reverse_distances = distance_matrix[:, j]
            best_reverse = np.argmin(reverse_distances)
            
            if best_reverse == i:
                cross_checked.append(match)
        
        return cross_checked
