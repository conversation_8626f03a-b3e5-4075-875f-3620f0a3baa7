#!/usr/bin/env python3
"""
Enhanced Optical Flow Estimator for ELOPE Challenge.

This module implements lightweight optical flow refinement techniques
that improve flow estimation accuracy, particularly in edge regions,
without the computational overhead of deep learning approaches.
"""

import numpy as np
import cv2
from typing import Dict, List, Tuple, Any, Optional
from scipy import ndimage
from scipy.spatial.distance import cdist
import warnings
warnings.filterwarnings('ignore')

class EdgeAwareFlowRefinement:
    """
    Edge-aware optical flow refinement using traditional computer vision techniques.
    
    Provides:
    1. Edge-preserving flow smoothing
    2. Occlusion detection and handling
    3. Flow consistency checking
    4. Subpixel refinement
    """
    
    def __init__(self):
        """Initialize edge-aware flow refinement."""
        
        # Edge detection parameters
        self.edge_params = {
            'canny_low': 50,
            'canny_high': 150,
            'sobel_kernel': 3,
            'edge_dilation': 2
        }
        
        # Flow refinement parameters
        self.refinement_params = {
            'bilateral_d': 9,
            'bilateral_sigma_color': 75,
            'bilateral_sigma_space': 75,
            'median_kernel': 5,
            'consistency_threshold': 1.0,
            'occlusion_threshold': 2.0,
            'subpixel_window': 5
        }
        
        # Flow validation parameters
        self.validation_params = {
            'magnitude_threshold': 50.0,
            'gradient_threshold': 10.0,
            'smoothness_weight': 0.3,
            'edge_preservation_weight': 0.7
        }
    
    def refine_optical_flow(self, flow: np.ndarray, 
                           frame1: np.ndarray, 
                           frame2: np.ndarray) -> Dict[str, Any]:
        """
        Refine optical flow with edge-aware techniques.
        
        Args:
            flow: Input optical flow field (H, W, 2)
            frame1: First frame
            frame2: Second frame
            
        Returns:
            Dictionary with refined flow and quality metrics
        """
        
        if flow.size == 0:
            return {
                'refined_flow': flow,
                'quality_map': np.zeros(flow.shape[:2]),
                'edge_map': np.zeros(flow.shape[:2]),
                'occlusion_map': np.zeros(flow.shape[:2]),
                'refinement_applied': False
            }
        
        # Convert frames to grayscale if needed
        gray1 = cv2.cvtColor(frame1, cv2.COLOR_BGR2GRAY) if len(frame1.shape) == 3 else frame1
        gray2 = cv2.cvtColor(frame2, cv2.COLOR_BGR2GRAY) if len(frame2.shape) == 3 else frame2
        
        # 1. Detect edges for edge-aware processing
        edge_map = self._detect_edges(gray1)
        
        # 2. Detect occlusions
        occlusion_map = self._detect_occlusions(flow, gray1, gray2)
        
        # 3. Apply edge-aware smoothing
        smoothed_flow = self._apply_edge_aware_smoothing(flow, edge_map)
        
        # 4. Handle occlusions
        occlusion_handled_flow = self._handle_occlusions(smoothed_flow, occlusion_map)
        
        # 5. Subpixel refinement
        refined_flow = self._subpixel_refinement(occlusion_handled_flow, gray1, gray2)
        
        # 6. Flow consistency validation
        validated_flow = self._validate_flow_consistency(refined_flow, gray1, gray2)
        
        # 7. Compute quality map
        quality_map = self._compute_flow_quality(validated_flow, edge_map, occlusion_map)
        
        return {
            'refined_flow': validated_flow,
            'quality_map': quality_map,
            'edge_map': edge_map,
            'occlusion_map': occlusion_map,
            'refinement_applied': True,
            'refinement_stats': {
                'edge_pixels': np.sum(edge_map > 0),
                'occluded_pixels': np.sum(occlusion_map > 0),
                'average_quality': np.mean(quality_map),
                'flow_magnitude_mean': np.mean(np.linalg.norm(validated_flow, axis=2)),
                'flow_magnitude_std': np.std(np.linalg.norm(validated_flow, axis=2))
            }
        }
    
    def _detect_edges(self, frame: np.ndarray) -> np.ndarray:
        """Detect edges using multi-scale edge detection."""
        
        # Canny edge detection
        canny_edges = cv2.Canny(frame, 
                               self.edge_params['canny_low'],
                               self.edge_params['canny_high'])
        
        # Sobel edge detection for gradient information
        sobel_x = cv2.Sobel(frame, cv2.CV_64F, 1, 0, 
                           ksize=self.edge_params['sobel_kernel'])
        sobel_y = cv2.Sobel(frame, cv2.CV_64F, 0, 1, 
                           ksize=self.edge_params['sobel_kernel'])
        sobel_magnitude = np.sqrt(sobel_x**2 + sobel_y**2)
        
        # Combine edge detections
        sobel_normalized = (sobel_magnitude / np.max(sobel_magnitude) * 255).astype(np.uint8)
        sobel_threshold = (sobel_normalized > 100).astype(np.uint8) * 255
        combined_edges = cv2.bitwise_or(canny_edges, sobel_threshold)
        
        # Dilate edges for edge-aware processing
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, 
                                          (self.edge_params['edge_dilation'], 
                                           self.edge_params['edge_dilation']))
        dilated_edges = cv2.dilate(combined_edges, kernel, iterations=1)
        
        return dilated_edges.astype(np.float32) / 255.0
    
    def _detect_occlusions(self, flow: np.ndarray, 
                          frame1: np.ndarray, 
                          frame2: np.ndarray) -> np.ndarray:
        """Detect occlusions using forward-backward consistency."""
        
        h, w = flow.shape[:2]
        
        # Forward flow (frame1 -> frame2)
        forward_flow = flow
        
        # Estimate backward flow (frame2 -> frame1) using forward flow
        backward_flow = np.zeros_like(forward_flow)
        
        # Simple backward flow estimation
        for y in range(h):
            for x in range(w):
                fx, fy = forward_flow[y, x]
                new_x, new_y = x + fx, y + fy
                
                if 0 <= new_x < w and 0 <= new_y < h:
                    # Approximate backward flow
                    backward_flow[int(new_y), int(new_x)] = [-fx, -fy]
        
        # Forward-backward consistency check
        consistency_error = np.zeros((h, w))
        
        for y in range(h):
            for x in range(w):
                fx, fy = forward_flow[y, x]
                new_x, new_y = x + fx, y + fy
                
                if 0 <= new_x < w and 0 <= new_y < h:
                    bx, by = backward_flow[int(new_y), int(new_x)]
                    consistency_error[y, x] = np.sqrt((fx + bx)**2 + (fy + by)**2)
        
        # Occlusion detection based on consistency threshold
        occlusion_map = consistency_error > self.refinement_params['occlusion_threshold']
        
        return occlusion_map.astype(np.float32)
    
    def _apply_edge_aware_smoothing(self, flow: np.ndarray, 
                                   edge_map: np.ndarray) -> np.ndarray:
        """Apply edge-aware smoothing to optical flow."""
        
        # Separate flow components
        flow_x = flow[:, :, 0]
        flow_y = flow[:, :, 1]
        
        # Convert to uint8 for bilateral filtering
        flow_x_norm = ((flow_x - np.min(flow_x)) / 
                      (np.max(flow_x) - np.min(flow_x) + 1e-8) * 255).astype(np.uint8)
        flow_y_norm = ((flow_y - np.min(flow_y)) / 
                      (np.max(flow_y) - np.min(flow_y) + 1e-8) * 255).astype(np.uint8)
        
        # Apply bilateral filtering for edge-aware smoothing
        smoothed_x = cv2.bilateralFilter(flow_x_norm,
                                        self.refinement_params['bilateral_d'],
                                        self.refinement_params['bilateral_sigma_color'],
                                        self.refinement_params['bilateral_sigma_space'])
        
        smoothed_y = cv2.bilateralFilter(flow_y_norm,
                                        self.refinement_params['bilateral_d'],
                                        self.refinement_params['bilateral_sigma_color'],
                                        self.refinement_params['bilateral_sigma_space'])
        
        # Convert back to original range
        smoothed_x = (smoothed_x.astype(np.float32) / 255.0 * 
                     (np.max(flow_x) - np.min(flow_x)) + np.min(flow_x))
        smoothed_y = (smoothed_y.astype(np.float32) / 255.0 * 
                     (np.max(flow_y) - np.min(flow_y)) + np.min(flow_y))
        
        # Preserve edges by blending original and smoothed flow
        edge_weight = edge_map[:, :, np.newaxis]
        smoothed_flow = np.stack([smoothed_x, smoothed_y], axis=2)
        
        # Blend: more smoothing in non-edge areas, preserve original in edge areas
        refined_flow = (edge_weight * flow + 
                       (1 - edge_weight) * smoothed_flow)
        
        return refined_flow
    
    def _handle_occlusions(self, flow: np.ndarray, 
                          occlusion_map: np.ndarray) -> np.ndarray:
        """Handle occluded regions in optical flow."""
        
        # Create a copy of the flow
        handled_flow = flow.copy()
        
        # Find occluded pixels
        occluded_pixels = occlusion_map > 0.5
        
        if np.sum(occluded_pixels) == 0:
            return handled_flow
        
        # Inpaint occluded regions using surrounding flow
        for channel in range(2):
            flow_channel = handled_flow[:, :, channel]
            
            # Create mask for inpainting (occluded areas)
            mask = (occluded_pixels * 255).astype(np.uint8)
            
            # Convert flow to uint8 for inpainting
            flow_min, flow_max = np.min(flow_channel), np.max(flow_channel)
            flow_range = flow_max - flow_min + 1e-8
            flow_uint8 = ((flow_channel - flow_min) / flow_range * 255).astype(np.uint8)
            
            # Inpaint using Navier-Stokes method
            inpainted = cv2.inpaint(flow_uint8, mask, 3, cv2.INPAINT_NS)
            
            # Convert back to original range
            inpainted_flow = (inpainted.astype(np.float32) / 255.0 * flow_range + flow_min)
            
            # Update flow channel
            handled_flow[:, :, channel] = inpainted_flow
        
        return handled_flow
    
    def _subpixel_refinement(self, flow: np.ndarray, 
                            frame1: np.ndarray, 
                            frame2: np.ndarray) -> np.ndarray:
        """Apply subpixel refinement to optical flow."""
        
        refined_flow = flow.copy()
        h, w = flow.shape[:2]
        window_size = self.refinement_params['subpixel_window']
        
        # Sample points for refinement (every 4th pixel to reduce computation)
        sample_step = 4
        
        for y in range(window_size, h - window_size, sample_step):
            for x in range(window_size, w - window_size, sample_step):
                
                # Current flow estimate
                fx, fy = flow[y, x]
                
                # Extract template from frame1
                template = frame1[y-window_size:y+window_size+1, 
                                x-window_size:x+window_size+1]
                
                if template.size == 0:
                    continue
                
                # Search area in frame2
                search_x = int(x + fx)
                search_y = int(y + fy)
                
                search_size = window_size + 2
                x1 = max(0, search_x - search_size)
                x2 = min(w, search_x + search_size + 1)
                y1 = max(0, search_y - search_size)
                y2 = min(h, search_y + search_size + 1)
                
                search_area = frame2[y1:y2, x1:x2]
                
                if search_area.size == 0 or template.shape != (2*window_size+1, 2*window_size+1):
                    continue
                
                # Template matching for subpixel refinement
                try:
                    result = cv2.matchTemplate(search_area, template, cv2.TM_CCOEFF_NORMED)
                    
                    if result.size > 0:
                        _, max_val, _, max_loc = cv2.minMaxLoc(result)
                        
                        if max_val > 0.7:  # Good match threshold
                            # Subpixel location
                            refined_x = x1 + max_loc[0] - x
                            refined_y = y1 + max_loc[1] - y
                            
                            # Update flow with subpixel refinement
                            refined_flow[y, x] = [refined_x, refined_y]
                            
                except cv2.error:
                    continue
        
        # Interpolate refined flow to all pixels
        refined_flow = self._interpolate_sparse_refinement(refined_flow, flow, sample_step)
        
        return refined_flow
    
    def _interpolate_sparse_refinement(self, refined_flow: np.ndarray, 
                                      original_flow: np.ndarray, 
                                      sample_step: int) -> np.ndarray:
        """Interpolate sparse subpixel refinements to dense flow."""
        
        # Simple interpolation: use refined values where available, original elsewhere
        h, w = refined_flow.shape[:2]
        result_flow = original_flow.copy()
        
        # Apply Gaussian smoothing to blend refined and original flow
        for channel in range(2):
            refined_channel = refined_flow[:, :, channel]
            original_channel = original_flow[:, :, channel]
            
            # Create weight map for refined pixels
            weight_map = np.zeros((h, w))
            weight_map[::sample_step, ::sample_step] = 1.0
            
            # Smooth weight map
            weight_map = cv2.GaussianBlur(weight_map, (15, 15), 5.0)
            
            # Blend flows
            result_flow[:, :, channel] = (weight_map * refined_channel + 
                                        (1 - weight_map) * original_channel)
        
        return result_flow
    
    def _validate_flow_consistency(self, flow: np.ndarray, 
                                  frame1: np.ndarray, 
                                  frame2: np.ndarray) -> np.ndarray:
        """Validate and correct flow consistency."""
        
        validated_flow = flow.copy()
        h, w = flow.shape[:2]
        
        # Check flow magnitude consistency
        flow_magnitude = np.linalg.norm(flow, axis=2)
        magnitude_threshold = self.validation_params['magnitude_threshold']
        
        # Clip extreme flow values
        extreme_mask = flow_magnitude > magnitude_threshold
        if np.sum(extreme_mask) > 0:
            # Normalize extreme flows
            normalized_magnitude = np.clip(flow_magnitude, 0, magnitude_threshold)
            flow_direction = flow / (flow_magnitude[:, :, np.newaxis] + 1e-8)
            validated_flow[extreme_mask] = (flow_direction * 
                                          normalized_magnitude[:, :, np.newaxis])[extreme_mask]
        
        # Apply median filtering to remove outliers
        kernel_size = self.refinement_params['median_kernel']
        for channel in range(2):
            validated_flow[:, :, channel] = ndimage.median_filter(
                validated_flow[:, :, channel], size=kernel_size
            )
        
        return validated_flow
    
    def _compute_flow_quality(self, flow: np.ndarray, 
                             edge_map: np.ndarray, 
                             occlusion_map: np.ndarray) -> np.ndarray:
        """Compute quality map for optical flow."""
        
        h, w = flow.shape[:2]
        quality_map = np.ones((h, w))
        
        # Flow magnitude consistency
        flow_magnitude = np.linalg.norm(flow, axis=2)
        magnitude_quality = 1.0 / (1.0 + flow_magnitude / 20.0)  # Penalize large flows
        
        # Edge preservation quality
        edge_quality = 1.0 - edge_map * 0.3  # Slightly lower quality at edges
        
        # Occlusion quality
        occlusion_quality = 1.0 - occlusion_map * 0.5  # Lower quality in occluded areas
        
        # Local smoothness quality
        grad_x = np.gradient(flow_magnitude, axis=1)
        grad_y = np.gradient(flow_magnitude, axis=0)
        gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)
        smoothness_quality = 1.0 / (1.0 + gradient_magnitude / 5.0)
        
        # Combine quality metrics
        quality_map = (magnitude_quality * 0.3 + 
                      edge_quality * 0.2 + 
                      occlusion_quality * 0.3 + 
                      smoothness_quality * 0.2)
        
        return np.clip(quality_map, 0.0, 1.0)

class EnhancedOpticalFlowEstimator:
    """
    Enhanced optical flow estimator combining multiple refinement techniques.
    """
    
    def __init__(self):
        """Initialize enhanced optical flow estimator."""
        
        # Initialize refinement modules
        self.edge_aware_refinement = EdgeAwareFlowRefinement()
        
        # Flow estimation parameters
        self.flow_params = {
            'method': 'lucas_kanade',
            'pyramid_levels': 4,
            'window_size': (21, 21),
            'max_iterations': 30,
            'epsilon': 0.01
        }
        
        # Enhancement parameters
        self.enhancement_params = {
            'enable_edge_refinement': True,
            'enable_subpixel_refinement': True,
            'enable_occlusion_handling': True,
            'quality_threshold': 0.5
        }
    
    def estimate_enhanced_flow(self, frame1: np.ndarray, 
                              frame2: np.ndarray,
                              base_flow: Optional[np.ndarray] = None) -> Dict[str, Any]:
        """
        Estimate enhanced optical flow with multiple refinement techniques.
        
        Args:
            frame1: First frame
            frame2: Second frame
            base_flow: Optional base flow to refine (if None, compute from scratch)
            
        Returns:
            Dictionary with enhanced flow and quality metrics
        """
        
        # Compute base flow if not provided
        if base_flow is None:
            base_flow = self._compute_base_flow(frame1, frame2)
        
        if base_flow.size == 0:
            return {
                'enhanced_flow': base_flow,
                'quality_map': np.zeros(frame1.shape[:2]),
                'enhancement_applied': False,
                'enhancement_stats': {}
            }
        
        # Apply edge-aware refinement
        refinement_result = self.edge_aware_refinement.refine_optical_flow(
            base_flow, frame1, frame2
        )
        
        enhanced_flow = refinement_result['refined_flow']
        quality_map = refinement_result['quality_map']
        
        # Compute enhancement statistics
        enhancement_stats = {
            'base_flow_magnitude_mean': np.mean(np.linalg.norm(base_flow, axis=2)),
            'enhanced_flow_magnitude_mean': np.mean(np.linalg.norm(enhanced_flow, axis=2)),
            'quality_improvement': np.mean(quality_map),
            'refinement_stats': refinement_result['refinement_stats']
        }
        
        return {
            'enhanced_flow': enhanced_flow,
            'quality_map': quality_map,
            'edge_map': refinement_result['edge_map'],
            'occlusion_map': refinement_result['occlusion_map'],
            'enhancement_applied': True,
            'enhancement_stats': enhancement_stats
        }
    
    def _compute_base_flow(self, frame1: np.ndarray, frame2: np.ndarray) -> np.ndarray:
        """Compute base optical flow using Lucas-Kanade method."""
        
        # Convert to grayscale if needed
        gray1 = cv2.cvtColor(frame1, cv2.COLOR_BGR2GRAY) if len(frame1.shape) == 3 else frame1
        gray2 = cv2.cvtColor(frame2, cv2.COLOR_BGR2GRAY) if len(frame2.shape) == 3 else frame2
        
        # Compute dense optical flow using Farneback method
        flow = cv2.calcOpticalFlowPyrLK(
            gray1, gray2, None, None,
            winSize=self.flow_params['window_size'],
            maxLevel=self.flow_params['pyramid_levels'],
            criteria=(cv2.TERM_CRITERIA_EPS | cv2.TERM_CRITERIA_COUNT,
                     self.flow_params['max_iterations'],
                     self.flow_params['epsilon'])
        )
        
        # If PyrLK fails, use Farneback as fallback
        if flow is None or len(flow) < 3:
            flow = cv2.calcOpticalFlowPyrLK(gray1, gray2, 0.5, 3, 15, 3, 5, 1.2, 0)
            if flow is None:
                return np.zeros((gray1.shape[0], gray1.shape[1], 2))
        
        return flow if isinstance(flow, np.ndarray) else np.zeros((gray1.shape[0], gray1.shape[1], 2))
