#!/usr/bin/env python3
"""
Intelligent Sensor Fusion for ELOPE Challenge.

This module implements adaptive sensor fusion with dynamic weight adjustment
based on confidence metrics, sequence difficulty, and temporal consistency.
"""

import numpy as np
import cv2
from typing import Dict, List, Tuple, Any, Optional
from scipy import ndimage
from scipy.stats import zscore
import warnings
warnings.filterwarnings('ignore')

class IntelligentSensorFusion:
    """
    Intelligent sensor fusion with adaptive weighting based on:
    1. Multi-dimensional confidence metrics
    2. Sequence difficulty assessment
    3. Temporal consistency analysis
    4. Cross-sensor validation
    """
    
    def __init__(self):
        """Initialize intelligent sensor fusion."""
        
        # Base fusion weights (will be adapted dynamically)
        self.base_weights = {
            'optical_flow': 0.6,
            'imu': 0.3,
            'range_meter': 0.1
        }
        
        # Current adaptive weights
        self.current_weights = self.base_weights.copy()
        
        # Fusion parameters
        self.fusion_params = {
            # Confidence thresholds
            'high_confidence_threshold': 0.8,
            'medium_confidence_threshold': 0.5,
            'low_confidence_threshold': 0.2,
            
            # Sequence difficulty parameters
            'difficulty_window': 10,  # Frames to assess difficulty
            'high_difficulty_threshold': 150.0,  # RMSE threshold
            'medium_difficulty_threshold': 100.0,
            
            # Temporal consistency parameters
            'temporal_window': 5,
            'consistency_threshold': 0.7,
            
            # Cross-sensor validation
            'validation_threshold': 0.6,
            'outlier_threshold': 2.5,  # Z-score threshold
            
            # Adaptive learning
            'learning_rate': 0.1,
            'weight_momentum': 0.8
        }
        
        # Historical data for adaptive learning
        self.velocity_history = []
        self.confidence_history = []
        self.difficulty_history = []
        self.weight_history = []
        
        # Sensor performance tracking
        self.sensor_performance = {
            'optical_flow': {'accuracy': 0.7, 'reliability': 0.8},
            'imu': {'accuracy': 0.6, 'reliability': 0.9},
            'range_meter': {'accuracy': 0.8, 'reliability': 0.7}
        }
        
        # Cross-sensor validation cache
        self.validation_cache = []
        
    def fuse_motion_estimates_intelligent(self, 
                                        optical_flow_motion: Dict[str, Any],
                                        imu_data: Dict[str, Any],
                                        range_data: Dict[str, Any],
                                        sequence_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Intelligent sensor fusion with adaptive weighting.
        
        Args:
            optical_flow_motion: Optical flow motion estimate
            imu_data: IMU data and estimates
            range_data: Range meter data
            sequence_context: Context about current sequence (difficulty, etc.)
            
        Returns:
            Fused motion estimate with enhanced confidence
        """
        
        # Step 1: Extract sensor estimates and confidences
        sensor_estimates = self._extract_sensor_estimates(
            optical_flow_motion, imu_data, range_data
        )
        
        # Step 2: Assess sequence difficulty
        difficulty_metrics = self._assess_sequence_difficulty(
            sensor_estimates, sequence_context
        )
        
        # Step 3: Compute multi-dimensional confidence
        confidence_metrics = self._compute_confidence_metrics(
            sensor_estimates, difficulty_metrics
        )
        
        # Step 4: Perform cross-sensor validation
        validation_results = self._cross_sensor_validation(sensor_estimates)
        
        # Step 5: Adapt fusion weights intelligently
        adaptive_weights = self._adapt_fusion_weights(
            confidence_metrics, difficulty_metrics, validation_results
        )
        
        # Step 6: Perform intelligent fusion
        fused_estimate = self._perform_intelligent_fusion(
            sensor_estimates, adaptive_weights
        )
        
        # Step 7: Apply temporal consistency enhancement
        enhanced_estimate = self._apply_temporal_consistency(
            fused_estimate, confidence_metrics
        )
        
        # Step 8: Update learning and history
        self._update_learning_history(
            enhanced_estimate, confidence_metrics, adaptive_weights
        )
        
        return enhanced_estimate
    
    def _extract_sensor_estimates(self, optical_flow_motion: Dict[str, Any],
                                 imu_data: Dict[str, Any],
                                 range_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract and normalize sensor estimates."""
        
        # Optical flow estimate
        of_velocity = optical_flow_motion.get('translation', np.zeros(3))
        of_confidence = optical_flow_motion.get('confidence', 0.0)
        of_inlier_ratio = optical_flow_motion.get('inlier_ratio', 0.0)
        
        # IMU estimate
        imu_velocity = imu_data.get('velocity_estimate', np.zeros(3))
        imu_confidence = imu_data.get('confidence', 0.0)
        
        # Range meter estimate (primarily Z-component)
        range_velocity = np.array([0, 0, range_data.get('depth_change', 0)])
        range_confidence = range_data.get('confidence', 0.5)
        
        return {
            'optical_flow': {
                'velocity': of_velocity,
                'confidence': of_confidence,
                'inlier_ratio': of_inlier_ratio,
                'quality_metrics': optical_flow_motion.get('quality_metrics', {})
            },
            'imu': {
                'velocity': imu_velocity,
                'confidence': imu_confidence,
                'quality_metrics': imu_data.get('quality_metrics', {})
            },
            'range_meter': {
                'velocity': range_velocity,
                'confidence': range_confidence,
                'quality_metrics': range_data.get('quality_metrics', {})
            }
        }
    
    def _assess_sequence_difficulty(self, sensor_estimates: Dict[str, Any],
                                   sequence_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Assess current sequence difficulty for adaptive weighting."""
        
        # Extract velocity magnitudes
        of_magnitude = np.linalg.norm(sensor_estimates['optical_flow']['velocity'])
        imu_magnitude = np.linalg.norm(sensor_estimates['imu']['velocity'])
        
        # Velocity inconsistency (high inconsistency = high difficulty)
        velocity_inconsistency = abs(of_magnitude - imu_magnitude) / (of_magnitude + imu_magnitude + 1e-6)
        
        # Confidence inconsistency
        of_conf = sensor_estimates['optical_flow']['confidence']
        imu_conf = sensor_estimates['imu']['confidence']
        confidence_inconsistency = abs(of_conf - imu_conf)
        
        # Historical difficulty assessment
        if len(self.difficulty_history) > 0:
            recent_difficulty = np.mean(self.difficulty_history[-self.fusion_params['difficulty_window']:])
        else:
            recent_difficulty = 0.5  # Medium difficulty assumption
        
        # Sequence context difficulty (if provided)
        context_difficulty = 0.5
        if sequence_context:
            # Use sequence-specific metrics if available
            context_difficulty = sequence_context.get('estimated_difficulty', 0.5)
        
        # Combined difficulty score
        combined_difficulty = (
            velocity_inconsistency * 0.4 +
            confidence_inconsistency * 0.3 +
            recent_difficulty * 0.2 +
            context_difficulty * 0.1
        )
        
        # Classify difficulty level
        if combined_difficulty > 0.7:
            difficulty_level = 'high'
        elif combined_difficulty > 0.4:
            difficulty_level = 'medium'
        else:
            difficulty_level = 'low'
        
        return {
            'combined_difficulty': combined_difficulty,
            'difficulty_level': difficulty_level,
            'velocity_inconsistency': velocity_inconsistency,
            'confidence_inconsistency': confidence_inconsistency,
            'context_difficulty': context_difficulty
        }
    
    def _compute_confidence_metrics(self, sensor_estimates: Dict[str, Any],
                                   difficulty_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Compute multi-dimensional confidence metrics."""
        
        # Individual sensor confidences
        of_confidence = sensor_estimates['optical_flow']['confidence']
        imu_confidence = sensor_estimates['imu']['confidence']
        range_confidence = sensor_estimates['range_meter']['confidence']
        
        # Adjust confidences based on difficulty
        difficulty_factor = 1.0 - difficulty_metrics['combined_difficulty'] * 0.3
        
        adjusted_confidences = {
            'optical_flow': of_confidence * difficulty_factor,
            'imu': imu_confidence * difficulty_factor,
            'range_meter': range_confidence * difficulty_factor
        }
        
        # Cross-sensor confidence (agreement between sensors)
        of_vel = sensor_estimates['optical_flow']['velocity']
        imu_vel = sensor_estimates['imu']['velocity']
        
        # Velocity agreement (higher agreement = higher confidence)
        velocity_agreement = 1.0 / (1.0 + np.linalg.norm(of_vel - imu_vel) / 10.0)
        
        # Overall system confidence
        system_confidence = (
            np.mean(list(adjusted_confidences.values())) * 0.7 +
            velocity_agreement * 0.3
        )
        
        return {
            'individual_confidences': adjusted_confidences,
            'velocity_agreement': velocity_agreement,
            'system_confidence': system_confidence,
            'difficulty_adjusted': True
        }
    
    def _cross_sensor_validation(self, sensor_estimates: Dict[str, Any]) -> Dict[str, Any]:
        """Perform cross-sensor validation to detect outliers."""
        
        # Extract velocities
        velocities = {
            'optical_flow': sensor_estimates['optical_flow']['velocity'],
            'imu': sensor_estimates['imu']['velocity'],
            'range_meter': sensor_estimates['range_meter']['velocity']
        }
        
        # Compute pairwise agreements
        agreements = {}
        agreements['of_imu'] = 1.0 / (1.0 + np.linalg.norm(
            velocities['optical_flow'] - velocities['imu']
        ) / 10.0)
        
        agreements['of_range'] = 1.0 / (1.0 + abs(
            velocities['optical_flow'][2] - velocities['range_meter'][2]
        ) / 5.0)
        
        agreements['imu_range'] = 1.0 / (1.0 + abs(
            velocities['imu'][2] - velocities['range_meter'][2]
        ) / 5.0)
        
        # Overall validation score
        validation_score = np.mean(list(agreements.values()))
        
        # Detect outlier sensors
        outlier_sensors = []
        mean_agreement = validation_score
        
        for sensor in ['optical_flow', 'imu', 'range_meter']:
            sensor_agreements = []
            if sensor == 'optical_flow':
                sensor_agreements = [agreements['of_imu'], agreements['of_range']]
            elif sensor == 'imu':
                sensor_agreements = [agreements['of_imu'], agreements['imu_range']]
            else:  # range_meter
                sensor_agreements = [agreements['of_range'], agreements['imu_range']]
            
            if np.mean(sensor_agreements) < mean_agreement * 0.6:
                outlier_sensors.append(sensor)
        
        return {
            'validation_score': validation_score,
            'pairwise_agreements': agreements,
            'outlier_sensors': outlier_sensors,
            'validation_passed': validation_score > self.fusion_params['validation_threshold']
        }
    
    def _adapt_fusion_weights(self, confidence_metrics: Dict[str, Any],
                             difficulty_metrics: Dict[str, Any],
                             validation_results: Dict[str, Any]) -> Dict[str, float]:
        """Intelligently adapt fusion weights based on current conditions."""
        
        # Start with base weights
        adaptive_weights = self.base_weights.copy()
        
        # Adjust based on individual sensor confidences
        confidences = confidence_metrics['individual_confidences']
        
        # Boost high-confidence sensors
        for sensor in adaptive_weights:
            conf = confidences[sensor]
            if conf > self.fusion_params['high_confidence_threshold']:
                adaptive_weights[sensor] *= 1.3
            elif conf < self.fusion_params['low_confidence_threshold']:
                adaptive_weights[sensor] *= 0.7
        
        # Adjust based on difficulty level
        difficulty_level = difficulty_metrics['difficulty_level']
        
        if difficulty_level == 'high':
            # In high difficulty, rely more on range meter (most reliable)
            adaptive_weights['range_meter'] *= 1.5
            adaptive_weights['optical_flow'] *= 0.8
        elif difficulty_level == 'low':
            # In low difficulty, trust optical flow more
            adaptive_weights['optical_flow'] *= 1.2
            adaptive_weights['imu'] *= 0.9
        
        # Penalize outlier sensors
        for outlier_sensor in validation_results['outlier_sensors']:
            adaptive_weights[outlier_sensor] *= 0.5
        
        # Normalize weights
        total_weight = sum(adaptive_weights.values())
        if total_weight > 0:
            for sensor in adaptive_weights:
                adaptive_weights[sensor] /= total_weight
        
        # Apply momentum from previous weights
        if len(self.weight_history) > 0:
            prev_weights = self.weight_history[-1]
            momentum = self.fusion_params['weight_momentum']
            
            for sensor in adaptive_weights:
                adaptive_weights[sensor] = (
                    momentum * prev_weights.get(sensor, adaptive_weights[sensor]) +
                    (1 - momentum) * adaptive_weights[sensor]
                )
        
        return adaptive_weights

    def _perform_intelligent_fusion(self, sensor_estimates: Dict[str, Any],
                                   adaptive_weights: Dict[str, float]) -> Dict[str, Any]:
        """Perform intelligent fusion with adaptive weights."""

        # Extract velocities
        of_velocity = sensor_estimates['optical_flow']['velocity']
        imu_velocity = sensor_estimates['imu']['velocity']
        range_velocity = sensor_estimates['range_meter']['velocity']

        # Weighted fusion
        fused_velocity = (
            of_velocity * adaptive_weights['optical_flow'] +
            imu_velocity * adaptive_weights['imu'] +
            range_velocity * adaptive_weights['range_meter']
        )

        # Compute fused confidence
        individual_confidences = [
            sensor_estimates['optical_flow']['confidence'],
            sensor_estimates['imu']['confidence'],
            sensor_estimates['range_meter']['confidence']
        ]

        weights_list = [
            adaptive_weights['optical_flow'],
            adaptive_weights['imu'],
            adaptive_weights['range_meter']
        ]

        fused_confidence = np.average(individual_confidences, weights=weights_list)

        # Compute fusion quality metrics
        fusion_quality = self._compute_fusion_quality(
            sensor_estimates, adaptive_weights, fused_velocity
        )

        return {
            'velocity': fused_velocity,
            'confidence': fused_confidence,
            'adaptive_weights_used': adaptive_weights.copy(),
            'fusion_quality': fusion_quality,
            'sensor_contributions': {
                'optical_flow': of_velocity * adaptive_weights['optical_flow'],
                'imu': imu_velocity * adaptive_weights['imu'],
                'range_meter': range_velocity * adaptive_weights['range_meter']
            }
        }

    def _apply_temporal_consistency(self, fused_estimate: Dict[str, Any],
                                   confidence_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Apply temporal consistency enhancement."""

        current_velocity = fused_estimate['velocity']
        current_confidence = fused_estimate['confidence']

        if len(self.velocity_history) < 2:
            # Not enough history for temporal consistency
            return fused_estimate

        # Get recent velocity history
        recent_velocities = self.velocity_history[-self.fusion_params['temporal_window']:]
        recent_confidences = self.confidence_history[-self.fusion_params['temporal_window']:]

        # Compute temporal consistency
        velocity_variations = []
        for prev_vel in recent_velocities:
            variation = np.linalg.norm(current_velocity - prev_vel)
            velocity_variations.append(variation)

        mean_variation = np.mean(velocity_variations)
        temporal_consistency = 1.0 / (1.0 + mean_variation / 10.0)

        # Apply temporal smoothing if consistency is good
        if temporal_consistency > self.fusion_params['consistency_threshold']:
            # Weighted average with recent history
            all_velocities = recent_velocities + [current_velocity]
            all_confidences = recent_confidences + [current_confidence]

            # Higher weight for more recent and more confident estimates
            weights = []
            for i, conf in enumerate(all_confidences):
                recency_weight = (i + 1) / len(all_confidences)  # More recent = higher weight
                combined_weight = conf * recency_weight
                weights.append(combined_weight)

            # Normalize weights
            weights = np.array(weights)
            weights = weights / np.sum(weights)

            # Compute temporally smoothed velocity
            smoothed_velocity = np.average(all_velocities, weights=weights, axis=0)

            # Enhanced confidence due to temporal consistency
            enhanced_confidence = current_confidence * (0.8 + 0.2 * temporal_consistency)

            fused_estimate['velocity'] = smoothed_velocity
            fused_estimate['confidence'] = min(enhanced_confidence, 1.0)
            fused_estimate['temporal_consistency'] = temporal_consistency
            fused_estimate['temporal_smoothing_applied'] = True
        else:
            fused_estimate['temporal_consistency'] = temporal_consistency
            fused_estimate['temporal_smoothing_applied'] = False

        return fused_estimate

    def _compute_fusion_quality(self, sensor_estimates: Dict[str, Any],
                               adaptive_weights: Dict[str, float],
                               fused_velocity: np.ndarray) -> Dict[str, float]:
        """Compute quality metrics for the fusion result."""

        # Weight distribution quality (more balanced = higher quality)
        weight_values = list(adaptive_weights.values())
        weight_entropy = -np.sum([w * np.log(w + 1e-10) for w in weight_values])
        weight_balance = weight_entropy / np.log(len(weight_values))  # Normalized entropy

        # Sensor agreement quality
        velocities = [
            sensor_estimates['optical_flow']['velocity'],
            sensor_estimates['imu']['velocity'],
            sensor_estimates['range_meter']['velocity']
        ]

        agreements = []
        for i in range(len(velocities)):
            for j in range(i + 1, len(velocities)):
                agreement = 1.0 / (1.0 + np.linalg.norm(velocities[i] - velocities[j]) / 10.0)
                agreements.append(agreement)

        sensor_agreement = np.mean(agreements)

        # Confidence consistency
        confidences = [
            sensor_estimates['optical_flow']['confidence'],
            sensor_estimates['imu']['confidence'],
            sensor_estimates['range_meter']['confidence']
        ]
        confidence_std = np.std(confidences)
        confidence_consistency = 1.0 / (1.0 + confidence_std)

        # Overall fusion quality
        overall_quality = (
            weight_balance * 0.3 +
            sensor_agreement * 0.4 +
            confidence_consistency * 0.3
        )

        return {
            'weight_balance': weight_balance,
            'sensor_agreement': sensor_agreement,
            'confidence_consistency': confidence_consistency,
            'overall_quality': overall_quality
        }

    def _update_learning_history(self, enhanced_estimate: Dict[str, Any],
                                confidence_metrics: Dict[str, Any],
                                adaptive_weights: Dict[str, float]):
        """Update learning history for continuous improvement."""

        # Update velocity and confidence history
        self.velocity_history.append(enhanced_estimate['velocity'])
        self.confidence_history.append(enhanced_estimate['confidence'])
        self.weight_history.append(adaptive_weights.copy())

        # Update difficulty history
        system_confidence = confidence_metrics['system_confidence']
        estimated_difficulty = 1.0 - system_confidence  # Lower confidence = higher difficulty
        self.difficulty_history.append(estimated_difficulty)

        # Keep only recent history
        max_history = 50
        if len(self.velocity_history) > max_history:
            self.velocity_history = self.velocity_history[-max_history:]
            self.confidence_history = self.confidence_history[-max_history:]
            self.weight_history = self.weight_history[-max_history:]
            self.difficulty_history = self.difficulty_history[-max_history:]

        # Update sensor performance tracking
        self._update_sensor_performance(enhanced_estimate, adaptive_weights)

    def _update_sensor_performance(self, enhanced_estimate: Dict[str, Any],
                                  adaptive_weights: Dict[str, float]):
        """Update sensor performance tracking for learning."""

        # Simple performance update based on confidence and weight usage
        confidence = enhanced_estimate['confidence']

        for sensor in self.sensor_performance:
            weight_used = adaptive_weights[sensor]

            # Update reliability based on weight usage
            # High weight usage with high confidence = good reliability
            reliability_update = weight_used * confidence

            # Exponential moving average
            alpha = 0.1
            current_reliability = self.sensor_performance[sensor]['reliability']
            self.sensor_performance[sensor]['reliability'] = (
                alpha * reliability_update + (1 - alpha) * current_reliability
            )

    def get_fusion_statistics(self) -> Dict[str, Any]:
        """Get statistics about fusion performance."""

        if len(self.velocity_history) == 0:
            return {'status': 'no_data'}

        velocities = np.array(self.velocity_history)
        confidences = np.array(self.confidence_history)

        # Compute velocity statistics
        velocity_stats = {
            'mean_velocity': np.mean(velocities, axis=0),
            'std_velocity': np.std(velocities, axis=0),
            'velocity_magnitude_mean': np.mean(np.linalg.norm(velocities, axis=1)),
            'velocity_magnitude_std': np.std(np.linalg.norm(velocities, axis=1))
        }

        # Compute confidence statistics
        confidence_stats = {
            'mean_confidence': np.mean(confidences),
            'std_confidence': np.std(confidences),
            'min_confidence': np.min(confidences),
            'max_confidence': np.max(confidences)
        }

        # Compute weight usage statistics
        if len(self.weight_history) > 0:
            weight_stats = {}
            for sensor in ['optical_flow', 'imu', 'range_meter']:
                sensor_weights = [w[sensor] for w in self.weight_history]
                weight_stats[f'{sensor}_mean_weight'] = np.mean(sensor_weights)
                weight_stats[f'{sensor}_std_weight'] = np.std(sensor_weights)
        else:
            weight_stats = {}

        return {
            'velocity_stats': velocity_stats,
            'confidence_stats': confidence_stats,
            'weight_stats': weight_stats,
            'sensor_performance': self.sensor_performance.copy(),
            'num_estimates': len(self.velocity_history)
        }
