#!/usr/bin/env python3
"""
Temporal Constraint Optimizer for ELOPE Challenge.

This module implements Kalman filter-based temporal constraints to improve
motion estimation stability by leveraging motion continuity assumptions.
"""

import numpy as np
from typing import Dict, <PERSON>, Tuple, Any, Optional
import warnings
warnings.filterwarnings('ignore')

class KalmanMotionFilter:
    """
    Kalman filter for motion state estimation with temporal constraints.
    
    State vector: [vx, vy, vz, ax, ay, az] (velocity and acceleration)
    Measurement: [vx, vy, vz] (velocity from optical flow)
    """
    
    def __init__(self):
        """Initialize <PERSON><PERSON> filter for motion estimation."""
        
        # State dimension: 6 (velocity + acceleration)
        self.state_dim = 6
        # Measurement dimension: 3 (velocity only)
        self.measurement_dim = 3
        
        # Initialize state vector [vx, vy, vz, ax, ay, az]
        self.state = np.zeros(self.state_dim)
        
        # Filter parameters (initialize first)
        self.filter_params = {
            'max_velocity': 50.0,      # Maximum reasonable velocity (m/s)
            'max_acceleration': 20.0,   # Maximum reasonable acceleration (m/s²)
            'velocity_noise': 5.0,      # Process noise for velocity
            'acceleration_noise': 2.0,  # Process noise for acceleration
            'measurement_noise': 5.0,   # Measurement noise
            'outlier_threshold': 3.0,   # Mahalanobis distance threshold
            'min_confidence': 0.1       # Minimum measurement confidence to use
        }

        # State covariance matrix
        self.P = np.eye(self.state_dim) * 100.0  # High initial uncertainty

        # Process noise covariance
        self.Q = self._create_process_noise_matrix()

        # Measurement noise covariance
        self.R = np.eye(self.measurement_dim) * 25.0  # Measurement uncertainty

        # Measurement matrix (observe velocity only)
        self.H = np.zeros((self.measurement_dim, self.state_dim))
        self.H[:3, :3] = np.eye(3)  # Observe velocity components

        # State transition matrix (will be updated with dt)
        self.F = np.eye(self.state_dim)
        
        # History for adaptive tuning
        self.innovation_history = []
        self.confidence_history = []
        self.prediction_errors = []
        
        # Filter state
        self.is_initialized = False
        self.last_timestamp = None
        
    def _create_process_noise_matrix(self) -> np.ndarray:
        """Create process noise covariance matrix."""
        
        Q = np.zeros((self.state_dim, self.state_dim))
        
        # Velocity noise
        vel_noise = self.filter_params['velocity_noise'] ** 2
        Q[:3, :3] = np.eye(3) * vel_noise
        
        # Acceleration noise
        acc_noise = self.filter_params['acceleration_noise'] ** 2
        Q[3:, 3:] = np.eye(3) * acc_noise
        
        return Q
    
    def _update_state_transition_matrix(self, dt: float):
        """Update state transition matrix with time step."""
        
        # F matrix for constant acceleration model
        # [vx]   [1 0 0 dt  0  0] [vx]
        # [vy] = [0 1 0  0 dt  0] [vy]
        # [vz]   [0 0 1  0  0 dt] [vz]
        # [ax]   [0 0 0  1  0  0] [ax]
        # [ay]   [0 0 0  0  1  0] [ay]
        # [az]   [0 0 0  0  0  1] [az]
        
        self.F = np.eye(self.state_dim)
        self.F[:3, 3:] = np.eye(3) * dt  # Velocity += acceleration * dt
    
    def predict(self, dt: float) -> Dict[str, Any]:
        """Predict next state using motion model."""
        
        # Update state transition matrix
        self._update_state_transition_matrix(dt)
        
        # Predict state
        self.state = self.F @ self.state
        
        # Predict covariance
        self.P = self.F @ self.P @ self.F.T + self.Q * dt
        
        # Apply constraints
        self._apply_state_constraints()
        
        return {
            'predicted_velocity': self.state[:3].copy(),
            'predicted_acceleration': self.state[3:].copy(),
            'velocity_uncertainty': np.sqrt(np.diag(self.P[:3, :3])),
            'prediction_confidence': self._compute_prediction_confidence()
        }
    
    def update(self, measurement: np.ndarray, measurement_confidence: float,
               timestamp: float) -> Dict[str, Any]:
        """Update filter with new measurement."""
        
        if not self.is_initialized:
            return self._initialize_filter(measurement, measurement_confidence, timestamp)
        
        # Compute time step
        dt = timestamp - self.last_timestamp if self.last_timestamp else 0.1
        dt = np.clip(dt, 0.01, 1.0)  # Reasonable time step bounds
        
        # Predict step
        prediction_result = self.predict(dt)
        
        # Check measurement quality
        if measurement_confidence < self.filter_params['min_confidence']:
            # Skip update for low-confidence measurements
            self.last_timestamp = timestamp
            return {
                'filtered_velocity': self.state[:3].copy(),
                'filtered_acceleration': self.state[3:].copy(),
                'filter_confidence': prediction_result['prediction_confidence'],
                'measurement_used': False,
                'skip_reason': 'low_confidence'
            }
        
        # Innovation (measurement residual)
        innovation = measurement - self.H @ self.state
        
        # Innovation covariance
        S = self.H @ self.P @ self.H.T + self.R * (1.0 / measurement_confidence)
        
        # Outlier detection using Mahalanobis distance
        mahalanobis_dist = np.sqrt(innovation.T @ np.linalg.inv(S) @ innovation)
        
        if mahalanobis_dist > self.filter_params['outlier_threshold']:
            # Skip outlier measurements
            self.last_timestamp = timestamp
            return {
                'filtered_velocity': self.state[:3].copy(),
                'filtered_acceleration': self.state[3:].copy(),
                'filter_confidence': prediction_result['prediction_confidence'],
                'measurement_used': False,
                'skip_reason': 'outlier',
                'mahalanobis_distance': mahalanobis_dist
            }
        
        # Kalman gain
        K = self.P @ self.H.T @ np.linalg.inv(S)
        
        # Update state
        self.state = self.state + K @ innovation
        
        # Update covariance
        I_KH = np.eye(self.state_dim) - K @ self.H
        self.P = I_KH @ self.P @ I_KH.T + K @ self.R @ K.T
        
        # Apply constraints
        self._apply_state_constraints()
        
        # Update history
        self._update_history(innovation, measurement_confidence, mahalanobis_dist)
        
        # Update timestamp
        self.last_timestamp = timestamp
        
        return {
            'filtered_velocity': self.state[:3].copy(),
            'filtered_acceleration': self.state[3:].copy(),
            'filter_confidence': self._compute_filter_confidence(),
            'measurement_used': True,
            'innovation': innovation,
            'mahalanobis_distance': mahalanobis_dist,
            'kalman_gain': np.linalg.norm(K)
        }
    
    def _initialize_filter(self, measurement: np.ndarray, confidence: float,
                          timestamp: float) -> Dict[str, Any]:
        """Initialize filter with first measurement."""
        
        # Initialize velocity with measurement
        self.state[:3] = measurement
        self.state[3:] = 0.0  # Zero initial acceleration
        
        # Initialize covariance
        self.P[:3, :3] = np.eye(3) * (10.0 / confidence)  # Velocity uncertainty
        self.P[3:, 3:] = np.eye(3) * 100.0  # High acceleration uncertainty
        
        self.is_initialized = True
        self.last_timestamp = timestamp
        
        return {
            'filtered_velocity': self.state[:3].copy(),
            'filtered_acceleration': self.state[3:].copy(),
            'filter_confidence': confidence,
            'measurement_used': True,
            'initialization': True
        }
    
    def _apply_state_constraints(self):
        """Apply physical constraints to state."""
        
        # Constrain velocity
        velocity_magnitude = np.linalg.norm(self.state[:3])
        if velocity_magnitude > self.filter_params['max_velocity']:
            self.state[:3] *= self.filter_params['max_velocity'] / velocity_magnitude
        
        # Constrain acceleration
        acceleration_magnitude = np.linalg.norm(self.state[3:])
        if acceleration_magnitude > self.filter_params['max_acceleration']:
            self.state[3:] *= self.filter_params['max_acceleration'] / acceleration_magnitude
    
    def _compute_prediction_confidence(self) -> float:
        """Compute confidence in prediction based on uncertainty."""
        
        velocity_uncertainty = np.trace(self.P[:3, :3])
        max_uncertainty = 100.0  # Maximum reasonable uncertainty
        
        confidence = 1.0 - min(velocity_uncertainty / max_uncertainty, 1.0)
        return max(confidence, 0.1)
    
    def _compute_filter_confidence(self) -> float:
        """Compute overall filter confidence."""
        
        # Base confidence from state uncertainty
        base_confidence = self._compute_prediction_confidence()
        
        # Bonus from consistent innovations
        if len(self.innovation_history) > 5:
            innovation_consistency = 1.0 / (1.0 + np.std(self.innovation_history[-5:]))
            base_confidence *= (0.8 + 0.2 * innovation_consistency)
        
        # Bonus from measurement confidence history
        if len(self.confidence_history) > 3:
            avg_measurement_confidence = np.mean(self.confidence_history[-3:])
            base_confidence *= (0.7 + 0.3 * avg_measurement_confidence)
        
        return np.clip(base_confidence, 0.1, 1.0)
    
    def _update_history(self, innovation: np.ndarray, confidence: float,
                       mahalanobis_dist: float):
        """Update filter history for adaptive tuning."""
        
        # Update innovation history
        innovation_magnitude = np.linalg.norm(innovation)
        self.innovation_history.append(innovation_magnitude)
        
        # Update confidence history
        self.confidence_history.append(confidence)
        
        # Update prediction error history
        self.prediction_errors.append(mahalanobis_dist)
        
        # Keep only recent history
        max_history = 20
        if len(self.innovation_history) > max_history:
            self.innovation_history = self.innovation_history[-max_history:]
            self.confidence_history = self.confidence_history[-max_history:]
            self.prediction_errors = self.prediction_errors[-max_history:]
    
    def get_filter_statistics(self) -> Dict[str, Any]:
        """Get filter performance statistics."""
        
        if not self.is_initialized:
            return {'status': 'not_initialized'}
        
        stats = {
            'current_velocity': self.state[:3].copy(),
            'current_acceleration': self.state[3:].copy(),
            'velocity_uncertainty': np.sqrt(np.diag(self.P[:3, :3])),
            'acceleration_uncertainty': np.sqrt(np.diag(self.P[3:, 3:])),
            'filter_confidence': self._compute_filter_confidence()
        }
        
        if len(self.innovation_history) > 0:
            stats.update({
                'avg_innovation': np.mean(self.innovation_history),
                'innovation_std': np.std(self.innovation_history),
                'avg_measurement_confidence': np.mean(self.confidence_history),
                'avg_prediction_error': np.mean(self.prediction_errors)
            })
        
        return stats

class TemporalConstraintOptimizer:
    """
    Temporal constraint optimizer using Kalman filtering for motion estimation.
    
    Provides:
    1. Temporal smoothing of velocity estimates
    2. Outlier detection and rejection
    3. Uncertainty quantification
    4. Motion prediction
    """
    
    def __init__(self):
        """Initialize temporal constraint optimizer."""
        
        # Kalman filter for motion tracking
        self.kalman_filter = KalmanMotionFilter()
        
        # Optimization parameters
        self.optimizer_params = {
            'smoothing_window': 5,      # Window for temporal smoothing
            'outlier_rejection': True,   # Enable outlier rejection
            'prediction_horizon': 3,     # Steps ahead for prediction
            'confidence_weighting': True # Weight by measurement confidence
        }
        
        # History for temporal analysis
        self.velocity_history = []
        self.timestamp_history = []
        self.confidence_history = []
        
    def optimize_velocity_estimate(self, raw_velocity: np.ndarray,
                                  measurement_confidence: float,
                                  timestamp: float) -> Dict[str, Any]:
        """
        Optimize velocity estimate using temporal constraints.
        
        Args:
            raw_velocity: Raw velocity estimate from optical flow
            measurement_confidence: Confidence in the measurement
            timestamp: Measurement timestamp
            
        Returns:
            Dictionary with optimized velocity and metadata
        """
        
        # Update Kalman filter
        filter_result = self.kalman_filter.update(
            raw_velocity, measurement_confidence, timestamp
        )
        
        # Update history
        self.velocity_history.append(raw_velocity)
        self.timestamp_history.append(timestamp)
        self.confidence_history.append(measurement_confidence)
        
        # Keep only recent history
        max_history = 50
        if len(self.velocity_history) > max_history:
            self.velocity_history = self.velocity_history[-max_history:]
            self.timestamp_history = self.timestamp_history[-max_history:]
            self.confidence_history = self.confidence_history[-max_history:]
        
        # Additional temporal smoothing if enabled
        if self.optimizer_params['smoothing_window'] > 1:
            smoothed_velocity = self._apply_temporal_smoothing(
                filter_result['filtered_velocity']
            )
        else:
            smoothed_velocity = filter_result['filtered_velocity']
        
        # Compute optimization quality metrics
        optimization_quality = self._assess_optimization_quality(filter_result)
        
        return {
            'optimized_velocity': smoothed_velocity,
            'raw_velocity': raw_velocity,
            'filter_result': filter_result,
            'optimization_quality': optimization_quality,
            'temporal_consistency': self._compute_temporal_consistency(),
            'prediction_available': self.kalman_filter.is_initialized
        }
    
    def _apply_temporal_smoothing(self, filtered_velocity: np.ndarray) -> np.ndarray:
        """Apply additional temporal smoothing to filtered velocity."""
        
        window_size = self.optimizer_params['smoothing_window']
        
        if len(self.velocity_history) < window_size:
            return filtered_velocity
        
        # Get recent velocities and confidences
        recent_velocities = self.velocity_history[-window_size:]
        recent_confidences = self.confidence_history[-window_size:]
        
        # Add current filtered velocity
        all_velocities = recent_velocities + [filtered_velocity]
        all_confidences = recent_confidences + [1.0]  # High confidence for filtered
        
        if self.optimizer_params['confidence_weighting']:
            # Weighted average by confidence
            weights = np.array(all_confidences)
            weights = weights / np.sum(weights)
            smoothed = np.average(all_velocities, weights=weights, axis=0)
        else:
            # Simple average
            smoothed = np.mean(all_velocities, axis=0)
        
        return smoothed
    
    def _assess_optimization_quality(self, filter_result: Dict[str, Any]) -> Dict[str, float]:
        """Assess the quality of temporal optimization."""
        
        # Filter confidence
        filter_confidence = filter_result.get('filter_confidence', 0.5)
        
        # Measurement usage rate
        measurement_used = filter_result.get('measurement_used', True)
        usage_bonus = 1.0 if measurement_used else 0.5
        
        # Innovation consistency (lower is better)
        innovation = filter_result.get('innovation', np.zeros(3))
        innovation_penalty = 1.0 / (1.0 + np.linalg.norm(innovation) / 10.0)
        
        # Overall quality
        overall_quality = filter_confidence * usage_bonus * innovation_penalty
        
        return {
            'filter_confidence': filter_confidence,
            'measurement_usage': usage_bonus,
            'innovation_consistency': innovation_penalty,
            'overall_quality': overall_quality
        }
    
    def _compute_temporal_consistency(self) -> float:
        """Compute temporal consistency of velocity estimates."""
        
        if len(self.velocity_history) < 3:
            return 0.5
        
        # Compute velocity variations
        recent_velocities = self.velocity_history[-5:]
        variations = []
        
        for i in range(1, len(recent_velocities)):
            variation = np.linalg.norm(recent_velocities[i] - recent_velocities[i-1])
            variations.append(variation)
        
        # Lower variation = higher consistency
        mean_variation = np.mean(variations)
        consistency = 1.0 / (1.0 + mean_variation / 5.0)
        
        return consistency
    
    def predict_future_velocity(self, steps_ahead: int = 1) -> Dict[str, Any]:
        """Predict future velocity using motion model."""
        
        if not self.kalman_filter.is_initialized:
            return {'prediction_available': False}
        
        # Use average time step
        if len(self.timestamp_history) > 1:
            time_diffs = np.diff(self.timestamp_history[-10:])
            avg_dt = np.mean(time_diffs)
        else:
            avg_dt = 0.1
        
        # Predict multiple steps ahead
        predicted_velocities = []
        current_state = self.kalman_filter.state.copy()
        current_P = self.kalman_filter.P.copy()
        
        for step in range(steps_ahead):
            # Predict one step
            self.kalman_filter._update_state_transition_matrix(avg_dt)
            current_state = self.kalman_filter.F @ current_state
            current_P = self.kalman_filter.F @ current_P @ self.kalman_filter.F.T + self.kalman_filter.Q * avg_dt
            
            predicted_velocities.append(current_state[:3].copy())
        
        return {
            'prediction_available': True,
            'predicted_velocities': predicted_velocities,
            'prediction_uncertainty': np.sqrt(np.diag(current_P[:3, :3])),
            'prediction_horizon': steps_ahead,
            'time_step': avg_dt
        }
    
    def get_optimizer_statistics(self) -> Dict[str, Any]:
        """Get optimizer performance statistics."""
        
        stats = {
            'kalman_filter_stats': self.kalman_filter.get_filter_statistics(),
            'num_measurements': len(self.velocity_history),
            'temporal_consistency': self._compute_temporal_consistency()
        }
        
        if len(self.velocity_history) > 0:
            velocities = np.array(self.velocity_history)
            stats.update({
                'velocity_statistics': {
                    'mean': np.mean(velocities, axis=0),
                    'std': np.std(velocities, axis=0),
                    'magnitude_mean': np.mean(np.linalg.norm(velocities, axis=1)),
                    'magnitude_std': np.std(np.linalg.norm(velocities, axis=1))
                }
            })
        
        return stats
