#!/usr/bin/env python3
"""
VY Error Deep Analysis Script
深度分析VY分量误差模式，找出系统性偏差和改进机会
"""

import json
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import pandas as pd
from scipy import stats
from typing import Dict, List, Tuple, Any

class VYErrorAnalyzer:
    """VY误差深度分析器"""
    
    def __init__(self, report_path: str):
        """初始化分析器"""
        self.report_path = Path(report_path)
        self.data = self._load_data()
        self.sequences = self.data['sequence_details']
        
    def _load_data(self) -> Dict[str, Any]:
        """加载验证报告数据"""
        with open(self.report_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def analyze_vy_dominance(self) -> Dict[str, Any]:
        """分析VY分量主导性"""
        print("🔍 VY分量主导性分析")
        print("=" * 50)
        
        vy_errors = []
        vx_errors = []
        vz_errors = []
        total_errors = []
        vy_ratios = []
        
        for seq in self.sequences:
            vx = seq['component_rmse']['vx']
            vy = seq['component_rmse']['vy']
            vz = seq['component_rmse']['vz']
            total = seq['rmse']
            
            vx_errors.append(vx)
            vy_errors.append(vy)
            vz_errors.append(vz)
            total_errors.append(total)
            
            # 计算VY在总误差中的占比
            vy_ratio = (vy ** 2) / (vx ** 2 + vy ** 2 + vz ** 2)
            vy_ratios.append(vy_ratio)
        
        # 统计分析
        vy_stats = {
            'mean': np.mean(vy_errors),
            'std': np.std(vy_errors),
            'min': np.min(vy_errors),
            'max': np.max(vy_errors),
            'median': np.median(vy_errors),
            'q25': np.percentile(vy_errors, 25),
            'q75': np.percentile(vy_errors, 75)
        }
        
        vy_ratio_stats = {
            'mean': np.mean(vy_ratios),
            'std': np.std(vy_ratios),
            'min': np.min(vy_ratios),
            'max': np.max(vy_ratios)
        }
        
        print(f"VY误差统计:")
        print(f"  平均值: {vy_stats['mean']:.2f}")
        print(f"  标准差: {vy_stats['std']:.2f}")
        print(f"  范围: [{vy_stats['min']:.2f}, {vy_stats['max']:.2f}]")
        print(f"  中位数: {vy_stats['median']:.2f}")
        print(f"  四分位数: [{vy_stats['q25']:.2f}, {vy_stats['q75']:.2f}]")
        
        print(f"\nVY占总误差比例:")
        print(f"  平均占比: {vy_ratio_stats['mean']:.1%}")
        print(f"  标准差: {vy_ratio_stats['std']:.1%}")
        print(f"  范围: [{vy_ratio_stats['min']:.1%}, {vy_ratio_stats['max']:.1%}]")
        
        # 找出VY主导的序列
        vy_dominant_seqs = []
        for i, ratio in enumerate(vy_ratios):
            if ratio > 0.5:  # VY占比超过50%
                vy_dominant_seqs.append({
                    'seq_id': i,
                    'vy_error': vy_errors[i],
                    'vy_ratio': ratio,
                    'total_error': total_errors[i]
                })
        
        print(f"\nVY主导序列 (占比>50%): {len(vy_dominant_seqs)}/{len(self.sequences)}")
        
        return {
            'vy_stats': vy_stats,
            'vy_ratio_stats': vy_ratio_stats,
            'vy_dominant_sequences': vy_dominant_seqs,
            'all_vy_errors': vy_errors,
            'all_vy_ratios': vy_ratios
        }
    
    def analyze_error_patterns(self) -> Dict[str, Any]:
        """分析误差模式和异常值"""
        print("\n🔍 误差模式分析")
        print("=" * 50)
        
        vy_errors = [seq['component_rmse']['vy'] for seq in self.sequences]
        seq_ids = [seq['sequence_id'] for seq in self.sequences]
        
        # 异常值检测 (使用IQR方法)
        q1 = np.percentile(vy_errors, 25)
        q3 = np.percentile(vy_errors, 75)
        iqr = q3 - q1
        lower_bound = q1 - 1.5 * iqr
        upper_bound = q3 + 1.5 * iqr
        
        outliers = []
        for i, error in enumerate(vy_errors):
            if error < lower_bound or error > upper_bound:
                outliers.append({
                    'seq_id': seq_ids[i],
                    'vy_error': error,
                    'type': 'high' if error > upper_bound else 'low'
                })
        
        print(f"异常值检测 (IQR方法):")
        print(f"  正常范围: [{lower_bound:.2f}, {upper_bound:.2f}]")
        print(f"  异常值数量: {len(outliers)}")
        
        for outlier in outliers:
            print(f"    序列{outlier['seq_id']}: {outlier['vy_error']:.2f} ({outlier['type']})")
        
        # 分布分析
        shapiro_stat, shapiro_p = stats.shapiro(vy_errors)
        print(f"\n分布正态性检验 (Shapiro-Wilk):")
        print(f"  统计量: {shapiro_stat:.4f}")
        print(f"  p值: {shapiro_p:.4f}")
        print(f"  是否正态分布: {'否' if shapiro_p < 0.05 else '是'}")
        
        return {
            'outliers': outliers,
            'bounds': {'lower': lower_bound, 'upper': upper_bound},
            'normality': {'statistic': shapiro_stat, 'p_value': shapiro_p}
        }
    
    def analyze_correlation_with_metadata(self) -> Dict[str, Any]:
        """分析VY误差与序列元数据的相关性"""
        print("\n🔍 VY误差与元数据相关性分析")
        print("=" * 50)
        
        # 提取数据
        vy_errors = []
        processing_times = []
        data_lengths = []
        events_counts = []
        
        for seq in self.sequences:
            vy_errors.append(seq['component_rmse']['vy'])
            processing_times.append(seq['processing_time'])
            data_lengths.append(seq['data_length'])
            events_counts.append(seq['events_count'])
        
        # 计算相关系数
        correlations = {}
        
        # VY误差 vs 处理时间
        corr_time, p_time = stats.pearsonr(vy_errors, processing_times)
        correlations['processing_time'] = {'correlation': corr_time, 'p_value': p_time}
        
        # VY误差 vs 事件数量
        corr_events, p_events = stats.pearsonr(vy_errors, events_counts)
        correlations['events_count'] = {'correlation': corr_events, 'p_value': p_events}
        
        # VY误差 vs 数据长度
        corr_length, p_length = stats.pearsonr(vy_errors, data_lengths)
        correlations['data_length'] = {'correlation': corr_length, 'p_value': p_length}
        
        print("相关性分析结果:")
        for key, corr_data in correlations.items():
            corr = corr_data['correlation']
            p_val = corr_data['p_value']
            significance = "显著" if p_val < 0.05 else "不显著"
            print(f"  VY误差 vs {key}: r={corr:.3f}, p={p_val:.3f} ({significance})")
        
        # 事件密度分析
        event_densities = [events / length for events, length in zip(events_counts, data_lengths)]
        corr_density, p_density = stats.pearsonr(vy_errors, event_densities)
        correlations['event_density'] = {'correlation': corr_density, 'p_value': p_density}
        
        print(f"  VY误差 vs 事件密度: r={corr_density:.3f}, p={p_density:.3f}")
        
        return {
            'correlations': correlations,
            'event_densities': event_densities
        }
    
    def identify_improvement_opportunities(self) -> Dict[str, Any]:
        """识别改进机会"""
        print("\n💡 改进机会识别")
        print("=" * 50)
        
        vy_errors = [seq['component_rmse']['vy'] for seq in self.sequences]
        
        # 按VY误差分组
        low_error_threshold = np.percentile(vy_errors, 25)
        high_error_threshold = np.percentile(vy_errors, 75)
        
        low_error_seqs = []
        medium_error_seqs = []
        high_error_seqs = []
        
        for i, seq in enumerate(self.sequences):
            vy_error = seq['component_rmse']['vy']
            seq_data = {
                'seq_id': seq['sequence_id'],
                'vy_error': vy_error,
                'events_count': seq['events_count'],
                'processing_time': seq['processing_time']
            }
            
            if vy_error <= low_error_threshold:
                low_error_seqs.append(seq_data)
            elif vy_error >= high_error_threshold:
                high_error_seqs.append(seq_data)
            else:
                medium_error_seqs.append(seq_data)
        
        print(f"序列分组 (按VY误差):")
        print(f"  低误差组 (≤{low_error_threshold:.1f}): {len(low_error_seqs)}个序列")
        print(f"  中误差组: {len(medium_error_seqs)}个序列")
        print(f"  高误差组 (≥{high_error_threshold:.1f}): {len(high_error_seqs)}个序列")
        
        # 分析低误差序列的特征
        if low_error_seqs:
            low_events = [seq['events_count'] for seq in low_error_seqs]
            low_times = [seq['processing_time'] for seq in low_error_seqs]
            
            print(f"\n低误差序列特征:")
            print(f"  平均事件数: {np.mean(low_events):.0f}")
            print(f"  平均处理时间: {np.mean(low_times):.1f}s")
        
        # 分析高误差序列的特征
        if high_error_seqs:
            high_events = [seq['events_count'] for seq in high_error_seqs]
            high_times = [seq['processing_time'] for seq in high_error_seqs]
            
            print(f"\n高误差序列特征:")
            print(f"  平均事件数: {np.mean(high_events):.0f}")
            print(f"  平均处理时间: {np.mean(high_times):.1f}s")
        
        # 潜在改进策略
        improvement_strategies = []
        
        # 策略1: 针对高误差序列的特殊处理
        if len(high_error_seqs) > 0:
            improvement_strategies.append({
                'strategy': 'high_error_specialization',
                'description': '为高误差序列开发专门的VY估计算法',
                'target_sequences': [seq['seq_id'] for seq in high_error_seqs],
                'potential_impact': 'high'
            })
        
        # 策略2: 基于事件密度的自适应处理
        improvement_strategies.append({
            'strategy': 'adaptive_event_density',
            'description': '根据事件密度调整VY估计参数',
            'potential_impact': 'medium'
        })
        
        # 策略3: VY分量物理约束
        improvement_strategies.append({
            'strategy': 'physics_constraints',
            'description': '利用月球着陆的物理约束限制VY估计范围',
            'potential_impact': 'high'
        })
        
        print(f"\n推荐改进策略:")
        for i, strategy in enumerate(improvement_strategies, 1):
            print(f"  {i}. {strategy['description']}")
            print(f"     潜在影响: {strategy['potential_impact']}")
        
        return {
            'error_groups': {
                'low': low_error_seqs,
                'medium': medium_error_seqs,
                'high': high_error_seqs
            },
            'thresholds': {
                'low': low_error_threshold,
                'high': high_error_threshold
            },
            'improvement_strategies': improvement_strategies
        }

def main():
    """主函数"""
    analyzer = VYErrorAnalyzer('full_training_validation_report.json')
    
    # 生成综合分析报告
    dominance_analysis = analyzer.analyze_vy_dominance()
    pattern_analysis = analyzer.analyze_error_patterns()
    correlation_analysis = analyzer.analyze_correlation_with_metadata()
    improvement_analysis = analyzer.identify_improvement_opportunities()
    
    print("\n📊 综合分析结论")
    print("=" * 50)
    
    avg_vy_ratio = dominance_analysis['vy_ratio_stats']['mean']
    print(f"1. VY分量平均占总误差的 {avg_vy_ratio:.1%}，确实是主要误差源")
    
    outlier_count = len(pattern_analysis['outliers'])
    print(f"2. 发现 {outlier_count} 个VY误差异常序列，需要特殊处理")
    
    significant_correlations = []
    for key, corr_data in correlation_analysis['correlations'].items():
        if corr_data['p_value'] < 0.05:
            significant_correlations.append(key)
    
    if significant_correlations:
        print(f"3. VY误差与以下因素显著相关: {', '.join(significant_correlations)}")
    else:
        print("3. VY误差与序列元数据无显著相关性，可能存在算法层面的系统性问题")
    
    high_error_count = len(improvement_analysis['error_groups']['high'])
    print(f"4. {high_error_count} 个高误差序列是优化重点目标")

if __name__ == "__main__":
    main()
